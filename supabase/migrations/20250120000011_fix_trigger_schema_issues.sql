-- Fix trigger schema issues
-- This migration fixes the database triggers that are causing "record has no field" errors

-- ============================================================================
-- 1. DROP EXISTING PROBLEMATIC TRIGGERS
-- ============================================================================

-- Drop existing triggers that are causing issues
DROP TRIGGER IF EXISTS trigger_collections_stats ON public.collections;
DROP TRIGGER IF EXISTS trigger_collection_cards_stats ON public.collection_cards;
DROP TRIGGER IF EXISTS trigger_wishlist_cards_stats ON public.wishlist_cards;

-- Drop the problematic function
DROP FUNCTION IF EXISTS trigger_update_user_statistics();

-- ============================================================================
-- 2. CREATE FIXED TRIGGER FUNCTION
-- ============================================================================

-- Create a new trigger function that properly handles the schema
CREATE OR REPLACE FUNCTION trigger_update_user_statistics()
RETURNS TRIGGER AS $$
DECLARE
    target_user_id UUID;
BEGIN
    -- Determine the user_id based on the table and operation
    IF TG_OP = 'INSERT' THEN
        CASE TG_TABLE_NAME
            WHEN 'collections' THEN
                target_user_id := NEW.user_id;
            WHEN 'wishlist_cards' THEN
                target_user_id := NEW.user_id;
            WHEN 'collection_cards' THEN
                -- Get user_id from the collections table
                SELECT c.user_id INTO target_user_id 
                FROM collections c 
                WHERE c.id = NEW.collection_id;
        END CASE;
        
        -- Update statistics for the target user
        IF target_user_id IS NOT NULL THEN
            PERFORM update_user_statistics(target_user_id);
        END IF;
        
        RETURN NEW;
        
    ELSIF TG_OP = 'DELETE' THEN
        CASE TG_TABLE_NAME
            WHEN 'collections' THEN
                target_user_id := OLD.user_id;
            WHEN 'wishlist_cards' THEN
                target_user_id := OLD.user_id;
            WHEN 'collection_cards' THEN
                -- Get user_id from the collections table
                SELECT c.user_id INTO target_user_id 
                FROM collections c 
                WHERE c.id = OLD.collection_id;
        END CASE;
        
        -- Update statistics for the target user
        IF target_user_id IS NOT NULL THEN
            PERFORM update_user_statistics(target_user_id);
        END IF;
        
        RETURN OLD;
        
    ELSIF TG_OP = 'UPDATE' THEN
        CASE TG_TABLE_NAME
            WHEN 'collections' THEN
                target_user_id := NEW.user_id;
            WHEN 'wishlist_cards' THEN
                target_user_id := NEW.user_id;
            WHEN 'collection_cards' THEN
                -- Get user_id from the collections table
                SELECT c.user_id INTO target_user_id 
                FROM collections c 
                WHERE c.id = NEW.collection_id;
        END CASE;
        
        -- Update statistics for the target user
        IF target_user_id IS NOT NULL THEN
            PERFORM update_user_statistics(target_user_id);
        END IF;
        
        RETURN NEW;
    END IF;
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- 3. RECREATE TRIGGERS WITH PROPER CONFIGURATION
-- ============================================================================

-- Create triggers for automatic statistics updates
CREATE TRIGGER trigger_collections_stats
    AFTER INSERT OR UPDATE OR DELETE ON public.collections
    FOR EACH ROW EXECUTE FUNCTION trigger_update_user_statistics();

CREATE TRIGGER trigger_collection_cards_stats
    AFTER INSERT OR UPDATE OR DELETE ON public.collection_cards
    FOR EACH ROW EXECUTE FUNCTION trigger_update_user_statistics();

CREATE TRIGGER trigger_wishlist_cards_stats
    AFTER INSERT OR UPDATE OR DELETE ON public.wishlist_cards
    FOR EACH ROW EXECUTE FUNCTION trigger_update_user_statistics();

-- ============================================================================
-- 4. GRANT PERMISSIONS
-- ============================================================================

-- Grant execute permissions on the trigger function
GRANT EXECUTE ON FUNCTION trigger_update_user_statistics() TO authenticated;
GRANT EXECUTE ON FUNCTION trigger_update_user_statistics() TO service_role;

-- ============================================================================
-- 5. REFRESH STATISTICS
-- ============================================================================

-- Update statistics for all existing users to ensure consistency
DO $$
DECLARE
    user_record RECORD;
BEGIN
    FOR user_record IN SELECT id FROM public.users LOOP
        PERFORM update_user_statistics(user_record.id);
    END LOOP;
END $$;

-- Add comment
COMMENT ON FUNCTION trigger_update_user_statistics() IS 'Fixed trigger function that properly handles schema differences between tables';

-- Refresh the schema cache
NOTIFY pgrst, 'reload schema';
