{"common": {"processing": "Procesando...", "loading": "Cargando...", "search": "Buscar", "back": "Volver", "edit": "<PERSON><PERSON>", "delete": "Eliminar", "save": "Guardar Cambios", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "Confirmar", "add": "<PERSON><PERSON><PERSON>", "remove": "Eliminar", "create": "<PERSON><PERSON><PERSON>", "update": "Actualizar", "close": "<PERSON><PERSON><PERSON>", "yes": "Sí", "no": "No", "success": "Éxito", "error": "Error", "warning": "Advertencia", "info": "Información", "understood": "Entendido", "tryAgainLater": "Por favor, intenta de nuevo más tarde", "openMenu": "<PERSON><PERSON><PERSON>", "verifying": "Verificando...", "unlimited": "<PERSON><PERSON><PERSON><PERSON>", "unknownCard": "¿Quién es ese Pokémon?", "unknownSet": "Set Desconocido", "cardUnavailable": "Datos de la carta no disponibles", "cardUnavailableExplanation": "Esta carta puede estar temporalmente no disponible o no existir en nuestra base de datos. Aún puedes añadirla a tu colección o lista de deseos.", "errorLoadingCard": "Error al cargar los datos de la carta"}, "cookies": {"title": "Consentimiento de Cookies", "description": "Utilizamos cookies para mejorar tu experiencia de navegación, analizar el tráfico del sitio y personalizar el contenido. Al hacer clic en 'Aceptar Todo', consientes el uso de cookies.", "learnMore": "Más información sobre cookies", "acceptAll": "<PERSON><PERSON><PERSON>", "rejectAll": "<PERSON><PERSON><PERSON>", "savePreferences": "Guardar Preferencias", "preferences": {"title": "Preferencias de Cookies", "description": "Gestiona tus preferencias de cookies. Puedes activar o desactivar diferentes tipos de cookies a continuación.", "necessary": {"title": "Cookies Necesarias", "description": "Estas cookies son esenciales para el funcionamiento correcto del sitio web.", "details": "Las cookies necesarias ayudan a hacer que un sitio web sea utilizable al habilitar funciones básicas como la navegación de páginas y el acceso a áreas seguras del sitio web. El sitio web no puede funcionar correctamente sin estas cookies.", "services": "Utilizamos Supabase para la autenticación de usuarios y Stripe para el procesamiento de pagos. Estos servicios utilizan cookies esenciales para proporcionar su funcionalidad."}, "analytics": {"title": "Cookies Analíticas", "description": "Estas cookies nos ayudan a entender cómo los visitantes interactúan con nuestro sitio web.", "details": "Las cookies analíticas ayudan a los propietarios de sitios web a comprender cómo los visitantes interactúan con los sitios web recopilando y reportando información de forma anónima. Utilizamos Google Analytics para recopilar información sobre cómo los usuarios utilizan nuestro sitio."}, "details": {"title": "Información Detallada de Cookies", "description": "A continuación puedes encontrar más información sobre las cookies específicas que utilizamos."}}}, "cookiePolicy": {"title": "Política de Cookies", "introduction": "Esta Política de Cookies explica cómo PokéCollector utiliza cookies y tecnologías similares para reconocerte cuando visitas nuestro sitio web. Explica qué son estas tecnologías y por qué las usamos, así como tus derechos para controlar nuestro uso de ellas.", "whatAreCookies": {"title": "¿Qué Son las Cookies?", "description": "Las cookies son pequeños archivos de datos que se colocan en tu ordenador o dispositivo móvil cuando visitas un sitio web. Las cookies son ampliamente utilizadas por los propietarios de sitios web para hacer que sus sitios funcionen, o para que funcionen de manera más eficiente, así como para proporcionar información de reportes. Las cookies establecidas por el propietario del sitio web (en este caso, PokéCollector) se denominan 'cookies de primera parte'. Las cookies establecidas por partes distintas al propietario del sitio web se denominan 'cookies de terceros'. Las cookies de terceros permiten que funciones o funcionalidades de terceros se proporcionen en o a través del sitio web (por ejemplo, publicidad, contenido interactivo y análisis)."}, "typesOfCookies": {"title": "Tipos de Cookies que Utilizamos"}, "howWeUse": {"title": "Cómo Utilizamos las Cookies", "description": "Utilizamos cookies por varias razones. Algunas cookies son necesarias por razones técnicas para que nuestro sitio web funcione, y nos referimos a ellas como cookies 'necesarias'. Otras cookies también nos permiten rastrear y dirigir los intereses de nuestros usuarios para mejorar la experiencia en nuestro sitio web. Terceros sirven cookies a través de nuestro sitio web para análisis y otros propósitos."}, "googleAnalytics": {"title": "Google Analytics", "description": "Utilizamos Google Analytics para ayudarnos a entender cómo nuestros clientes utilizan el sitio. Puedes leer más sobre cómo Google utiliza tu Información Personal aquí: https://policies.google.com/privacy. Puedes optar por no participar en el seguimiento de Google Analytics desactivando las cookies analíticas en tus preferencias de cookies."}, "stripe": {"title": "Stripe", "description": "Utilizamos Stripe para el procesamiento de pagos. Stripe utiliza cookies para habilitar la funcionalidad de pago, prevenir fraudes y mejorar la seguridad de nuestro procesamiento de pagos. Estas cookies son necesarias para el correcto funcionamiento de las características de pago."}, "supabase": {"title": "Supabase", "description": "Utilizamos Supabase para la autenticación de usuarios y el almacenamiento de datos. Supabase utiliza cookies y almacenamiento local para mantener tu estado de autenticación y asegurar que permanezcas conectado. Estas cookies son necesarias para el correcto funcionamiento de las características de autenticación."}, "managingCookies": {"title": "Gestión de tus Preferencias de Cookies", "description": "Puedes establecer o modificar tus preferencias de cookies en cualquier momento utilizando nuestro centro de preferencias de cookies. Esto te permite rechazar cookies no esenciales."}, "manageCookies": "Gestionar Preferencias de Cookies", "browserSettings": {"title": "Controles del Navegador", "description": "La mayoría de los navegadores web también permiten cierto control de la mayoría de las cookies a través de la configuración del navegador. Para obtener más información sobre las cookies, incluido cómo ver qué cookies se han establecido, visita www.aboutcookies.org o www.allaboutcookies.org. Descubre cómo gestionar las cookies en navegadores populares: Google Chrome, Microsoft Edge, Mozilla Firefox, Microsoft Internet Explorer, Opera, Apple Safari."}, "updates": {"title": "Actualizaciones de esta Política de Cookies", "description": "Podemos actualizar esta Política de Cookies de vez en cuando para reflejar, por ejemplo, cambios en las cookies que utilizamos o por otras razones operativas, legales o regulatorias. Por lo tanto, revisa regularmente esta Política de Cookies para mantenerte informado sobre nuestro uso de cookies y tecnologías relacionadas."}, "contact": {"title": "Contáctanos", "description": "Si tienes alguna pregunta sobre nuestro uso de cookies u otras tecnologías, por favor envíanos un correo electró<NAME_EMAIL>."}, "lastUpdated": "Última actualización:"}, "privacyPolicy": {"title": "Política de Privacidad", "introduction": "Esta Política de Privacidad describe cómo se recopila, utiliza y comparte tu información personal cuando visitas o realizas una compra en PokéCollector. Respetamos tu privacidad y estamos comprometidos a proteger tus datos personales.", "lastUpdated": "Última actualización:", "informationWeCollect": {"title": "Información que Recopilamos", "description": "Cuando visitas el sitio, recopilamos automáticamente cierta información sobre tu dispositivo, incluyendo información sobre tu navegador web, dirección IP, zona horaria y algunas de las cookies que están instaladas en tu dispositivo."}, "personalInformation": {"title": "Información Personal", "description": "Cuando te registras para una cuenta o te suscribes a nuestro servicio, podemos pedirte que proporciones los siguientes tipos de información personal:", "items": {"email": "Dirección de correo electrónico", "name": "Nombre", "billingInfo": "Información de facturación (para suscripciones de pago)"}}, "usageData": {"title": "Datos de Uso", "description": "También podemos recopilar información sobre cómo accedes y utilizas nuestro servicio:", "items": {"ipAddress": "Dirección IP", "browserType": "Tipo y versión del navegador", "pagesVisited": "Páginas de nuestro servicio que visitas", "timeSpent": "Tiempo dedicado a esas páginas"}}, "howWeUseInformation": {"title": "Cómo Utilizamos tu Información", "description": "Utilizamos la información que recopilamos sobre ti para:", "items": {"provideService": "Pro<PERSON><PERSON>ar, operar y mantener nuestro servicio", "improveService": "Mejorar y personalizar tu experiencia en nuestro sitio", "communicate": "Comunicarnos contigo, incluso para atención al cliente", "processPayments": "Procesar pagos y prevenir fraudes"}}, "dataStorage": {"title": "Dón<PERSON> se Almacenan tus Datos", "description": "Tus datos se almacenan de forma segura con nuestros proveedores de servicios:"}, "supabase": {"title": "Supabase", "description": "Utilizamos Supabase para almacenar datos de usuarios e información de autenticación. Supabase almacena datos de acuerdo con los requisitos del RGPD e implementa medidas de seguridad apropiadas para proteger tu información personal."}, "stripe": {"title": "Stripe", "description": "Para el procesamiento de pagos, utilizamos Stripe. Cuando proporcionas tu información de pago, se transmite directamente a Stripe y no se almacena en nuestros servidores. Stripe procesa tu información de pago de acuerdo con su política de privacidad."}, "cookies": {"title": "Cookies y Seguimiento", "description": "Utilizamos cookies y tecnologías de seguimiento similares para rastrear la actividad en nuestro servicio y almacenar cierta información. Puedes instruir a tu navegador para que rechace todas las cookies o para que indique cuándo se está enviando una cookie.", "linkText": "Más información sobre nuestra Política de Cookies"}, "dataSharing": {"title": "Compartir tu Información Personal", "description": "Compartimos tu Información Personal con terceros para ayudarnos a utilizar tu Información Personal, como se describió anteriormente. También podemos compartir tu Información Personal en las siguientes situaciones:", "items": {"serviceProviders": "Con proveedores de servicios que realizan servicios para nosotros", "legalRequirements": "Si lo exige la ley o en respuesta a solicitudes válidas de autoridades públicas", "businessTransfers": "En relación con, o durante las negociaciones de, cualquier fusión, venta de activos de la empresa, financiación o adquisición"}}, "dataRetention": {"title": "Retención de Datos", "description": "Conservaremos tu Información Personal solo durante el tiempo necesario para los fines establecidos en esta Política de Privacidad. Conservaremos y utilizaremos tu Información Personal en la medida necesaria para cumplir con nuestras obligaciones legales, resolver disputas y hacer cumplir nuestros acuerdos y políticas legales."}, "yourRights": {"title": "Tus Derechos bajo el RGPD", "description": "Si eres residente del Espacio Económico Europeo (EEE), tienes ciertos derechos de protección de datos. Tienes derecho a:", "items": {"access": "Acceder a la información que tenemos sobre ti", "rectification": "Rectificar cualquier información personal inexacta o incompleta", "erasure": "Solicitar la eliminación de tus datos personales", "restriction": "Restringir u objetar el procesamiento de tus datos personales", "dataPortability": "Solicitar la transferencia de tus datos a ti o a un tercero", "objection": "Oponerte al procesamiento de tus datos personales para marketing directo"}, "exerciseRights": "Para ejercer estos derechos, por favor contáctanos utilizando la información de contacto proporcionada a continuación."}, "dataProtectionOfficer": {"title": "Delegado de Protección de Datos", "description": "Hemos designado un delegado de protección de datos (DPD) que es responsable de supervisar las cuestiones relacionadas con esta política de privacidad. Si tienes alguna pregunta sobre esta política de privacidad, por favor contacta a nuestro <NAME_EMAIL>."}, "changes": {"title": "Cambios a esta Política de Privacidad", "description": "Podemos actualizar nuestra Política de Privacidad de vez en cuando. Te notificaremos cualquier cambio publicando la nueva Política de Privacidad en esta página y actualizando la fecha de 'última actualización'."}, "contact": {"title": "Contáctanos", "description": "Si tienes alguna pregunta sobre esta Política de Privacidad, por favor contáctanos."}}, "termsOfService": {"title": "Términos de Servicio", "introduction": "Estos Términos de Servicio rigen tu uso del sitio web y servicio de PokéCollector. Al acceder o utilizar nuestro servicio, aceptas estar sujeto a estos Términos.", "lastUpdated": "Última actualización:", "definitions": {"title": "Definiciones", "items": {"service": {"term": "<PERSON><PERSON><PERSON>", "definition": "El sitio web y plataforma PokéCollector operado por nosotros."}, "user": {"term": "Usuario", "definition": "Cualquier individuo que accede o utiliza el Servicio."}, "account": {"term": "C<PERSON><PERSON>", "definition": "Una cuenta única creada para ti para acceder a nuestro Servicio o partes de nuestro Servicio."}, "subscription": {"term": "Suscripción", "definition": "El plan de pago recurrente que otorga acceso a las características premium del Servicio."}, "company": {"term": "Compañía", "definition": "Se refiere a PokéCollector, el operador de este Servicio."}}}, "acceptance": {"title": "Aceptación de Términos", "description": "Al acceder o utilizar nuestro Servicio, confirmas que has leído, entendido y aceptas estar sujeto a estos Términos de Servicio. Si no estás de acuerdo con alguna parte de estos términos, no puedes utilizar nuestro Servicio."}, "userAccounts": {"title": "Cuentas de Usuario", "description": "Cuando creas una cuenta con nosotros, debes proporcionar información que sea precisa, completa y actual en todo momento. El incumplimiento de esto constituye una violación de los Términos, lo que puede resultar en la terminación inmediata de tu cuenta en nuestro Servicio.", "items": {"accuracy": "Eres responsable de mantener la precisión de la información de tu cuenta.", "security": "Eres responsable de salvaguardar la contraseña utilizada para acceder al Servicio.", "unauthorized": "Aceptas notificarnos inmediatamente de cualquier acceso no autorizado o uso de tu cuenta.", "termination": "Nos reservamos el derecho de terminar o suspender tu cuenta a nuestra sola discreción, sin previo aviso."}}, "subscriptions": {"title": "Suscripciones", "description": "Algunas partes del Servicio están disponibles mediante suscripción. Se te facturará por adelantado de forma recurrente, dependiendo del tipo de plan de suscripción que selecciones.", "billing": "Al suscribirte a nuestro Servicio, nos autorizas a cargar tu método de pago de forma recurrente según los términos de tu plan de suscripción.", "cancellation": "Puedes cancelar tu suscripción en cualquier momento a través de la configuración de tu cuenta. Tu suscripción permanecerá activa hasta el final del período de facturación actual."}, "intellectualProperty": {"title": "Propiedad Intelectual", "description": "El Servicio y su contenido original, características y funcionalidad son y seguirán siendo propiedad exclusiva de PokéCollector y sus licenciantes. El Servicio está protegido por derechos de autor, marcas registradas y otras leyes.", "userContent": "Al enviar contenido al Servicio, nos otorgas una licencia mundial, no exclusiva, libre de regalías para usar, reproducir, modificar, adaptar, publicar, traducir y distribuir tu contenido en cualquier medio existente o futuro."}, "prohibitedUses": {"title": "<PERSON><PERSON>", "description": "Puedes utilizar nuestro Servicio solo para fines legales y de acuerdo con estos Términos. Aceptas no utilizar el Servicio:", "items": {"illegal": "De cualquier manera que viole cualquier ley o regulación nacional o internacional aplicable.", "harmful": "Para transmitir, o procurar el envío de, cualquier material publicitario o promocional, incluyendo cualquier 'correo basura', 'cadena de cartas', 'spam' o cualquier otra solicitud similar.", "impersonation": "Para hacerte pasar o intentar hacerte pasar por la Compañía, un empleado de la Compañía, otro usuario o cualquier otra persona o entidad.", "infringement": "Para participar en cualquier actividad que infrinja o se apropie indebidamente de los derechos de propiedad intelectual de otros.", "automation": "Para utilizar cualquier robot, araña u otro dispositivo automático para acceder al Servicio para cualquier propósito sin nuestro permiso expreso por escrito."}}, "disclaimer": {"title": "Descargo de Responsabilidad de Garantías", "description": "Tu uso del Servicio es bajo tu propio riesgo. El Servicio se proporciona 'TAL CUAL' y 'SEGÚN DISPONIBILIDAD'. El Servicio se proporciona sin garantías de ningún tipo, ya sean expresas o implícitas."}, "limitation": {"title": "Limitación de Responsabilidad", "description": "En ningún caso <PERSON>ollector, ni sus directores, empleados, socios, agentes, proveedores o afiliados, serán responsables por cualquier daño indirecto, incidental, especial, consecuente o punitivo, incluyendo sin limitación, pérdida de beneficios, datos, uso, buena voluntad u otras pérdidas intangibles, resultantes de tu acceso a o uso de o incapacidad para acceder o usar el Servicio."}, "indemnification": {"title": "Indemnización", "description": "Aceptas defender, indemnizar y mantener indemne a PokéCollector y sus licenciantes, proveedores de servicios, empleados, agentes, funcionarios y directores de y contra cualquier reclamo, responsabilidad, daño, juicio, premio, pérdida, costo, gasto o tarifa (incluyendo honorarios razonables de abogados) que surjan de o estén relacionados con tu violación de estos Términos o tu uso del Servicio."}, "termination": {"title": "Terminación", "description": "Podemos terminar o suspender tu cuenta inmediatamente, sin previo aviso o responsabilidad, por cualquier motivo, incluyendo sin limitación si incumples los Términos. Tras la terminación, tu derecho a utilizar el Servicio cesará inmediatamente."}, "governing": {"title": "Ley Aplicable", "description": "Estos Términos se regirán e interpretarán de acuerdo con las leyes de España, sin tener en cuenta sus disposiciones sobre conflictos de leyes."}, "changes": {"title": "Cambios a los Términos", "description": "Nos reservamos el derecho, a nuestra sola discreción, de modificar o reemplazar estos Términos en cualquier momento. Si una revisión es material, intentaremos proporcionar al menos 30 días de aviso antes de que los nuevos términos entren en vigor."}, "contact": {"title": "Contáctanos", "description": "Si tienes alguna pregunta sobre estos Términos, por favor contáctanos."}, "relatedPolicies": "Por favor, revisa también nuestras otras políticas:", "privacyPolicyLink": "Política de Privacidad", "cookiePolicyLink": "Política de Cookies"}, "navigation": {"dashboard": "Panel", "collection": "Colección", "wishlist": "Lista de deseos", "search": "Buscar", "account": "C<PERSON><PERSON>", "pricing": "<PERSON><PERSON><PERSON>", "rules": "Reg<PERSON>", "subscription": "Suscripción"}, "app": {"title": "PokéCollector", "subtitle": "Gestiona tu colección de cartas Pokémon", "description": "La mejor manera de organizar y gestionar tu colección de cartas Pokémon"}, "dashboard": {"title": "Mi Colección de Pokémon", "subtitle": "Gestiona tu colección de cartas Pokémon"}, "collection": {"loading": "Cargando co<PERSON>cci<PERSON>...", "searchInCollection": "Buscar cartas en esta colección", "noSearchResults": "No se encontraron cartas que coincidan con tu búsqueda.", "title": "Mi Colección", "myCollections": "Mis Cole<PERSON>", "default": "Predeterminada", "create": "<PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON>", "delete": "Elimina<PERSON>", "empty": "No tienes colecciones aún", "emptyDescription": "Crea tu primera colección para empezar a guardar tus cartas.", "createFirst": "Crear Mi Primera Colección", "confirmDelete": "¿Estás seguro de que quieres eliminar esta colección?", "deleteWarning": "Esta acción no se puede deshacer y eliminará todas las cartas de esta colección.", "editDescription": "Modifica los detalles de tu colección", "createDescription": "Crea una nueva colección para organizar tus cartas", "nameLabel": "Nombre de la Colección", "namePlaceholder": "Mi Colección Increíble", "descriptionLabel": "Descripción (Opcional)", "descriptionPlaceholder": "Describe tu colección...", "setAsDefault": "Establecer como colección predeterminada", "removeCard": "Eliminar <PERSON>", "cardAddedToDefault": "Carta añadida a la colección por defecto", "cardRemoved": "Carta eliminada de la colección", "collectionCreated": "Colección creada con éxito", "collectionUpdated": "Colección actualizada con éxito", "collectionDeleted": "Colección eliminada con éxito", "addToDefault": "Añadir a colección por defecto", "collectionDetails": "Detalles en tu colección", "defaultName": "Mi Colección", "defaultDescription": "Mi colección de cartas Pokémon", "updated": "Colección actualizada", "created": "Colección creada", "deleted": "Colección eliminada", "saveSuccess": "La colección \"{name}\" ha sido guardada exitosamente.", "deleteSuccess": "La colección ha sido eliminada.", "errors": {"loadFailed": "No se pudieron cargar las colecciones. Por favor, intenta de nuevo.", "createDefaultFailed": "No se pudo crear la colección por defecto. Por favor, intenta de nuevo.", "defaultExists": "Ya existe una colección predeterminada. Por favor, desmarca la colección predeterminada actual antes de establecer otra.", "saveFailed": "No se pudo guardar la colección. Por favor, intenta de nuevo.", "deleteFailed": "No se pudo eliminar la colección. Por favor, intenta de nuevo.", "setDefaultFailed": "No se pudo establecer la colección predeterminada.", "noSelectedCollection": "No hay una colección seleccionada", "createFailed": "No se pudo crear la colección. Por favor, intenta de nuevo.", "addFailed": "No se pudo añadir la carta a la colección. Por favor, intenta de nuevo."}, "confirmDeleteWithName": "¿Estás seguro de que quieres eliminar la colección \"{name}\"? Esta acción no se puede deshacer y todas las cartas asociadas serán eliminadas.", "addCard": "<PERSON><PERSON><PERSON> a Colección", "selectCollection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "adding": "Añadiendo...", "cardAdded": "Carta añadida a la colección", "cardAddedDescription": "La carta ha sido añadida a tu colección", "cardUpdatedDescription": "La carta ha sido actualizada en tu colección", "quantityIncreased": "La cantidad ha sido incrementada", "deleteConfirmTitle": "Elimina<PERSON>", "deleteConfirmDescription": "¿Estás seguro de que quieres eliminar la colección \"{name}\"?", "maxCards": "Máximo de cartas", "maxCollections": "Máximo de colecciones", "cards": "<PERSON><PERSON>", "collections": "Colecciones", "noDefaultCollection": "No hay colección predeterminada", "pleaseCreateDefault": "Por favor, crea una colección predeterminada o establece una existente como predeterminada.", "defaultCollectionNeeded": "Para usar la función de añadir rápido, necesitas tener una colección predeterminada.", "setExistingAsDefault": "Puedes establecer una de tus colecciones existentes como predeterminada:", "noCollectionsYet": "No tienes ninguna colección. Crea una nueva para empezar.", "createNew": "<PERSON><PERSON>r <PERSON> Colección"}, "wishlist": {"title": "Lista de Deseos", "empty": "Tu lista de deseos está vacía", "addCard": "Añadir a lista de deseos", "removeCard": "Eliminar de lista de deseos", "loading": "Cargando lista de deseos...", "emptyDescription": "Añade cartas a tu lista de deseos para seguir las que quieres conseguir", "searchCards": "Buscar cartas para añadir", "cardAdded": "Carta añadida a la lista de deseos", "addSuccess": "La carta ha sido añadida a tu lista de deseos", "cardRemoved": "Carta eliminada", "removeSuccess": "La carta ha sido eliminada de tu lista de deseos", "removedAfterAdding": "La carta ha sido eliminada de tu lista de deseos después de añadirla a tu colección", "removedFromWishlist": "Eliminada de la lista de deseos", "cardRemovedDescription": "La carta ha sido eliminada de tu lista de deseos", "alreadyInWishlist": "Ya en Lista de Deseos", "cardAlreadyExists": "Esta carta ya está en tu lista de deseos.", "errors": {"loadFailed": "No se pudo cargar la lista de deseos", "removeFailed": "No se pudo eliminar la carta de la lista de deseos", "addFailed": "No se pudo añadir la carta a la lista de deseos.", "idNotFound": "ID de lista de deseos no encontrado"}, "maxItems": "Máximo en lista de deseos"}, "search": {"cards": "Buscar Cartas", "title": "Buscar Cartas", "nameSearchPlaceholder": "Buscar por nombre...", "searching": "Buscando cartas...", "searchCards": "Buscar Cartas", "sortBy": "Ordenar por", "sortNameAsc": "Nombre (A-Z)", "sortNameDesc": "Nombre (Z-A)", "sortNumberAsc": "<PERSON><PERSON><PERSON><PERSON> (Menor a Mayor)", "sortNumberDesc": "<PERSON><PERSON><PERSON><PERSON> (Mayor a Menor)", "description": "Busca entre miles de cartas y encuentra las que necesitas para tu colección", "placeholder": "Buscar por nombre, número o set", "filters": "<PERSON><PERSON><PERSON>", "noResults": "No se encontraron resultados", "clearFilters": "Limpiar filtros", "applyFilters": "Aplicar filtros", "searchResults": "Resultados de búsqueda", "loadMore": "<PERSON>gar más", "errors": {"loadFailed": "No se pudieron cargar los resultados de la búsqueda", "filtersFailed": "No se pudieron cargar los filtros. Por favor, intenta de nuevo.", "setsFailed": "Error al cargar sets", "typesFailed": "Error al cargar tipos", "raritiesFailed": "Error al cargar rarezas", "filterDataFailed": "Error al cargar datos de filtros"}}, "filters": {"pokemonType": "Tipo de Pokémon", "allTypes": "Todos los tipos", "rarity": "<PERSON><PERSON>", "allRarities": "Todas las rarezas", "supertype": "Supertipo", "allSupertypes": "Todos los supertipos", "subtype": "Subtipo", "allSubtypes": "Todos los subtipos", "name": "Nombre", "set": "Set", "type": "Tipo"}, "card": {"details": "Detalles de la carta", "selectCondition": "Seleccionar estado", "name": "Nombre", "number": "Número", "set": "Set", "rarity": "<PERSON><PERSON>", "type": "Tipo", "supertype": "Supertipo", "subtype": "Subtipo", "condition": "Condición", "price": "Precio", "quantity": "Cantidad", "notes": "Notas", "personalNotes": "Notas personales sobre esta carta", "addToCollection": "Añadir a la colección", "addToWishlist": "Añadir a la lista de deseos", "removeFromCollection": "Eliminar de la colección", "removeFromWishlist": "Eliminar de la lista de deseos", "abilities": "Habilidades", "attacks": "Ataques", "rules": "Reg<PERSON>", "weaknesses": "Debilidades", "resistances": "Resistencias", "retreatCost": "Coste de retirada", "foil": "Foil/Holo", "firstEdition": "Primera Edición", "conditionNearMint": "<PERSON>asi Perfecta", "removed": "Carta eliminada", "updated": "Carta actualizada", "removeSuccess": "La carta ha sido eliminada de tu colección.", "updateSuccess": "La carta ha sido actualizada.", "edit": "Editar carta", "editDescription": "Modifica los detalles de esta carta en tu colección", "notesPlaceholder": "Añade notas personales sobre esta carta (opcional)", "errors": {"removeFailed": "No se pudo eliminar la carta. Por favor, intenta de nuevo.", "updateFailed": "No se pudo actualizar la carta. Por favor, intenta de nuevo."}}, "limits": {"cards": "carta(s) en tu colección", "collections": "colecciones", "wishlist": "cartas en tu lista de deseos", "elements": "elementos", "limitReached": "Límite de {{type}} alcanzado", "currentPlanLimit": "Tu plan actual ({{plan}}) ha alcanzado su límite. Actualiza tu plan para obtener acceso a más {{type}} y otras funcionalidades.", "upgradePlan": "Mejorar Plan"}, "subscription": {"limitReachedMessage": "Has alcanzado el límite de {{limit}} {{type}} en tu plan {{plan}}", "loading": "Cargando suscripción...", "upgradePlan": "Mejorar Plan", "statusActive": "Activo", "statusInactive": "Inactivo", "viewPlans": "Ver planes", "title": "Gestión de Suscripción", "description": "Gestiona tu plan y uso", "currentPlan": "Plan actual", "cancelPlan": "Cancelar plan", "renewalDate": "Fecha de renovación", "paymentMethod": "Método de pago", "updatePayment": "Actualizar mé<PERSON> de pago", "billingHistory": "Historial de facturación", "planDetails": "Detalles del plan", "limitReached": "Has alcanzado el límite de tu plan", "upgradeNow": "<PERSON><PERSON><PERSON><PERSON>ora", "active": "Tu suscripción está activa", "inactive": "No tienes una suscripción activa", "plan": "Plan", "status": "Estado", "successTitle": "¡Felicidades, ahora eres un {{planName}} Pokémon!", "successDescription": "Tu suscripción ha sido procesada con éxito.", "verifying": "Verificando suscripción...", "verifyingStatus": "Verificando estado de suscripción...", "nextBilling": "Próximo cobro", "goToDashboard": "<PERSON><PERSON> <PERSON>", "yourSubscription": "Tu Suscripción", "currentPlanDetails": "Detalles de tu plan de suscripción actual", "currentPeriod": "Período Actual", "validUntil": "<PERSON><PERSON><PERSON><PERSON> hasta {{date}}", "includedFeatures": "Características Incluidas", "cancelSubscription": "Cancelar Suscripción", "cancelConfirmation": "¿Estás seguro de que deseas cancelar tu suscripción? Perderás acceso a las características premium al final de tu período de facturación actual.", "canceling": "Cancelando...", "canceled": "Suscripción Cancelada", "canceledDescription": "Tu suscripción ha sido cancelada con éxito. Tendrás acceso a las características premium hasta el final de tu período de facturación actual.", "statusCanceled": "Cancelada", "noActiveSubscription": "Sin Suscripción Activa", "considerSubscribing": "Considera suscribirte a un plan para acceder a las características premium.", "actions": {"addToWishlist": "añadir a tu lista de deseos", "addToCollection": "añadir cartas a tu colección", "createCollections": "crear nuevas co<PERSON>cciones"}, "loginRequiredForWishlist": "Debes iniciar sesión para añadir cartas a tu lista de deseos.", "loginRequiredForCollection": "Debes iniciar sesión para añadir cartas a tu colección.", "downgrade": {"confirmTitle": "Confirmar cambio de plan", "description": "Estás a punto de cambiar de {currentPlan} a {targetPlan}. Este cambio reducirá los límites de tu cuenta.", "warning": "Importante: <PERSON> excedes los límites del nuevo plan, no podrás añadir más items hasta que liberes espacio.", "confirm": "Confirmar cambio"}, "requiredTitle": "Suscripción Requerida", "requiredDescription": "Para acceder a {{action}}, necesitas actualizar tu plan de suscripción.", "welcomeMessage": "¡Bienvenido a PokéCollector!", "changePlan": "Cambiar Plan de Suscripción", "month": "mes", "processing": "Procesando...", "upgradeTo": "Actualizar a {{plan}}", "prorationAmount": "Monto a pagar por el cambio de plan: {{amount}}€", "planUpdated": "Plan actualizado", "planUpdatedSuccess": "Tu plan ha sido actualizado exitosamente", "errors": {"changePlan": "Error al cambiar plan", "changePlanLog": "Error al cambiar plan:", "tryAgain": "Hubo un error al procesar tu solicitud. Por favor, intenta nuevamente.", "noSubscriptionInfo": "No hay información de suscripción disponible", "noStripeId": "No se encontró ID de suscripción de Stripe", "cancelFailed": "Error al cancelar la suscripción. Por favor, intenta nuevamente.", "validationFailed": "Error al validar los límites de la suscripción"}, "planUsage": "Uso Actual del Plan", "upgradeExperience": "Mejora tu experiencia", "discoverPremium": "Descubre todas las características premium de PokéCollector.", "features": {"unlimitedCollections": "Colecciones ilimitadas", "extendedWishlist": "Lista de deseos extendida", "exclusiveFeatures": "Características premium exclusivas"}, "later": "<PERSON>ás tarde"}, "pricing": {"title": "Planes y Precios", "subtitle": "Estos son nuestros planes para ti. Empieza con el gratuito y sube de nivel cuando lo necesites", "startFree": "<PERSON><PERSON><PERSON>", "startFreeButton": "<PERSON><PERSON><PERSON>", "popular": "Popular", "popularBadge": "Popular", "currentPlan": "Plan actual", "alreadyActive": "Ya tienes este plan activo.", "updateError": "No se pudo actualizar el plan. Por favor, intenta de nuevo.", "faqTitle": "Preguntas Frecuentes", "currentPlanToast": {"title": "Plan actual", "description": "Ya tienes este plan activo."}, "errorUpdatingPlan": "No se pudo actualizar el plan. Por favor, intenta de nuevo.", "faq": {"freeplan": {"question": "¿Qué incluye el plan gratuito?", "answer": "El plan Aprendiz incluye todas las funciones básicas para empezar tu colección: hasta 50 cartas, una colección, 10 cartas en tu lista de deseos y búsqueda básica por nombre y tipo. Es perfecto para comenzar y familiarizarte con la plataforma."}, "changePlan": {"question": "¿Puedo cambiar de plan en cualquier momento?", "answer": "<PERSON><PERSON>, puedes actualizar o cambiar tu plan en cualquier momento. Si actualizas a un plan superior, tendrás acceso inmediato a todas las nuevas funciones. <PERSON> decides bajar de plan, el cambio se aplicará al final del período de facturación actual."}, "collections": {"question": "¿Cómo funciona el sistema de colecciones?", "answer": "Cada colección te permite organizar tus cartas Pokémon de la manera que prefieras. Puedes crear colecciones por set, por tipo, por rareza o cualquier otro criterio. Cada carta puede incluir detalles como su condición, notas personales y fecha de adquisición."}, "payment": {"question": "¿Qué métodos de pago aceptan?", "answer": "Aceptamos todas las tarjetas de crédito y débito principales (Visa, Mastercard, American Express) y PayPal. Todos los pagos se procesan de forma segura a través de Stripe."}, "cancel": {"question": "¿Puedo cancelar mi suscripción en cualquier momento?", "answer": "<PERSON><PERSON>, puedes cancelar tu suscripción cuando quieras. No hay compromisos de permanencia. Al cancelar, mantendrás el acceso a las funciones premium hasta el final del período facturado."}, "data": {"question": "¿Qué pasa con mis datos si cancelo mi suscripción?", "answer": "Si cancelas una suscripción premium y vuelves al plan gratuito, tus datos se mantendrán guardados, pero solo podrás acceder a las limitaciones del plan gratuito. Podrás volver a acceder a todos tus datos al reactivar tu suscripción."}, "wishlist": {"question": "¿Cómo funciona la lista de deseos?", "answer": "La lista de deseos te permite guardar las cartas que te gustaría adquirir en el futuro. Puedes añadir notas, establecer prioridades y recibir notificaciones cuando actualicemos información sobre esas cartas."}, "share": {"question": "¿Puedo compartir mi colección con otros usuarios?", "answer": "<PERSON><PERSON>, puedes compartir tu colección con otros usuarios mediante un enlace público. Tú controlas qué información es visible para otros coleccionistas y puedes desactivar la visibilidad en cualquier momento."}, "database": {"question": "¿Cómo mantienen actualizada la base de datos de cartas?", "answer": "Nuestra base de datos se actualiza regularmente con los últimos lanzamientos de cartas Pokémon. Trabajamos con fuentes oficiales y bases de datos reconocidas para garantizar que tengas acceso a la información más precisa y actualizada."}, "support": {"question": "¿Ofrecen soporte técnico?", "answer": "Sí, ofrecemos soporte técnico por email para todos los usuarios. Los usuarios de planes premium tienen acceso a soporte prioritario con tiempos de respuesta garantizados."}}, "perMonth": "mes", "selectPlan": "Seleccionar plan", "maestroFeatures": {"0": "Todo lo de Entrenador", "1": "Cartas ilimitadas", "2": "Colecciones ilimitadas", "3": "Lista de deseos ilimitada", "4": "Soporte Prioritario"}, "price": "Precio", "free": "<PERSON><PERSON><PERSON>"}, "plans": {"aprendiz": "Aprendiz", "entrenador": "Entrenador", "maestro": "Maestro", "descriptions": {"aprendiz": "Plan gratuito para comenzar", "entrenador": "Para coleccionistas serios", "maestro": "Para coleccionistas profesionales"}, "featuresList": {"aprendiz": ["Hasta 50 cartas", "1 colección", "10 cartas en lista de deseos", "Búsqueda de cartas por nombre y tipo"], "entrenador": ["Hasta 500 cartas", "5 colecciones", "50 cartas en lista de deseos", "Búsqueda avanzada de cartas por nombre, tipo, subtipo, rareza y más"], "maestro": ["Todo lo de Entrenador", "Cartas ilimitadas", "Colecciones ilimitadas", "Lista de deseos ilimitada", "Soporte Prioritario"]}, "perMonth": "mes", "selectPlan": "Seleccionar plan", "currentPlan": "Plan Actual", "startFree": "<PERSON><PERSON><PERSON>", "popular": "Popular", "alreadyActive": "Ya tienes este plan activo.", "updateError": "No se pudo actualizar el plan. Por favor, intenta de nuevo."}, "account": {"title": "Mi Cuenta", "profile": "Perfil", "security": "Seguridad", "deleteAccount": "Eliminar cuenta", "changePassword": "Cambiar contraseña", "dangerZone": "Zona de Peligro", "dangerZoneDescription": "Las acciones en esta sección son irreversibles y pueden resultar en pérdida de datos", "passwordResetConfirmation": "Te enviaremos un correo con instrucciones para cambiar tu contraseña. ¿Deseas continuar?", "instructionsSent": "Instrucciones enviadas", "passwordResetInstructions": "Hemos enviado un correo con instrucciones para cambiar tu contraseña. Por favor, revisa tu bandeja de entrada.", "deleteAccountConfirmation": "¿Estás seguro de que quieres eliminar tu cuenta? Esta acción es irreversible y perderás todas tus colecciones y datos.", "deleteAccountDescription": "Una vez que elimines tu cuenta, no hay vuelta atrás. Por favor, asegúrate de que realmente deseas proceder.", "settings": {"title": "Configuración de la cuenta", "profile": "Perfil", "security": "Seguridad y privacidad", "preferences": "Preferencias"}, "updateProfile": "Actualizar perfil", "changeEmail": "Cambiar email", "emailChanged": "Email cambiado exitosamente", "passwordChanged": "Contraseña cambiada exitosamente", "profileUpdated": "Perfil actualizado exitosamente", "noName": "Sin nombre", "nameUpdated": "Nombre actualizado", "nameUpdatedSuccess": "Tu nombre ha sido actualizado exitosamente", "nameUpdateError": "No se pudo actualizar el nombre", "passwordResetEmailSent": "Email enviado", "checkInbox": "Revisa tu bandeja de entrada para cambiar tu contraseña", "passwordResetError": "No se pudo enviar el correo de recuperación", "noActiveSession": "No hay sesión activa", "deleteError": "Error al eliminar la cuenta", "deleteAccountError": "Error al eliminar la cuenta", "statistics": "Estadísticas", "currentUsage": "Uso Actual", "cardsInCollections": "Cartas en Colecciones", "collections": "Colecciones", "wishlist": "Elementos en Lista de Deseos", "email": "Email", "name": "Nombre", "notSet": "No establecido", "emailSent": "Email enviado", "checkInboxPassword": "Revisa tu bandeja de entrada y sigue las instrucciones para establecer una nueva contraseña", "changePasswordDescription": "Te enviaremos un email con un enlace para cambiar tu contraseña", "sendResetEmail": "Enviar email de restablecimiento", "checkEmail": "Revisa tu email", "passwordEmailSent": "Hemos enviado un email a {{email}} con instrucciones para cambiar tu contraseña", "permanentlyDelete": "Eliminar permanentemente", "accountDeleted": "Cuenta eliminada", "accountDeleteSuccess": "Tu cuenta ha sido eliminada exitosamente", "dangerDescription": "Acciones irreversibles para tu cuenta", "errors": {"resetEmailFailed": "No se pudo enviar el correo de restablecimiento", "nameUpdateFailed": "No se pudo actualizar el nombre", "deleteFailed": "No se pudo eliminar la cuenta"}, "goodbye": "¡Hasta pronto!", "accountDeletedMessage": "Tu cuenta ha sido eliminada correctamente. Esperamos volver a verte pronto en PokéCollector.", "welcomeBackMessage": "Recuerda que siempre serás bienvenido/a de nuevo en nuestra comunidad de coleccionistas.", "backToHome": "Volver al inicio"}, "auth": {"login": "In<PERSON><PERSON>", "backToLogin": "Volver al inicio de sesión", "checkInboxAndFollow": "Revisa tu bandeja de entrada y sigue las instrucciones", "checkInboxForReset": "Revisa tu bandeja de entrada para restablecer tu contraseña", "signup": "Registrarse", "logout": "<PERSON><PERSON><PERSON>", "email": "Correo electrónico", "emailPlaceholder": "<EMAIL>", "emailSent": "Correo electrónico enviado", "errorTryAgain": "<PERSON><PERSON><PERSON>, por favor intenta de nuevo", "password": "Contraseña", "confirmPassword": "Confirmar con<PERSON>", "forgotPassword": "¿Olvidaste tu contraseña?", "resetPassword": "Restablecer contraseña", "instructionsSentTo": "Instrucciones enviadas a", "recoverPassword": "Recuperar contraseña", "confirmEmail": {"title": "Confirma tu email", "description": "Tu cuenta aún no ha sido verificada. Hemos enviado un nuevo email de confirmación a:", "checkInbox": "Por favor, revisa tu bandeja de entrada y carpeta de spam."}, "alreadyHaveAccount": "¿Ya tienes una cuenta?", "dontHaveAccount": "¿No tienes una cuenta?", "signupSuccess": "Registro exitoso. Por favor, verifica tu correo electrónico.", "loginSuccess": "Inicio de sesión exitoso", "logoutSuccess": "Cierre de sesión exitoso", "resetPasswordSuccess": "Contraseña restablecida con éxito", "emailConfirmSuccess": "Correo electrónico confirmado con éxito", "fullName": "Nombre completo", "fullNamePlaceholder": "Tu nombre", "createAccount": "<PERSON><PERSON><PERSON>", "verifyEmail": "Verifica tu Email", "verificationLinkSent": "Hemos enviado un enlace de verificación a:", "checkInboxInstructions": "Por favor, revisa tu bandeja de entrada y sigue las instrucciones para completar tu registro.", "openGmail": "<PERSON><PERSON><PERSON>", "loggingIn": "Iniciando se<PERSON>...", "accessingAccount": "Accediendo a tu cuenta...", "registering": "Registrando usuario...", "verifyNewEmail": "Verifica tu nuevo email", "confirmationLinkSent": "Hemos enviado un enlace de confirmación a:", "checkInboxForNewEmail": "Por favor, revisa tu bandeja de entrada y sigue las instrucciones para confirmar tu nuevo email.", "backToAccount": "Volver a mi cuenta", "dialog": {"title": "Únete a PokéCollector", "description": "Para gestionar tu colección y lista de deseos, necesitas tener una cuenta. ¡Es gratis y solo toma un minuto! Podrás:", "benefits": {"collection": "Crear y gestionar tu colección de cartas", "wishlist": "Guardar cartas en tu lista de deseos", "tracking": "<PERSON><PERSON> seguimiento de tus cartas favoritas"}}, "newPassword": "Nueva Contraseña", "passwordChanged": "Tu contraseña ha sido actualizada correctamente", "processingReset": "Enviando instrucciones...", "errors": {"passwordsDoNotMatch": "Las contraseñas no coinciden", "invalidPassword": "Contraseña inválida", "invalidCredentials": "Email o contraseña incorrectos", "loginError": "Error de inicio de sesión", "initError": "Error al inicializar usuario", "accountInitError": "Hubo un problema al inicializar tu cuenta.", "loginProcessError": "Error durante el inicio de sesión", "signupError": "Error de registro", "verificationError": "Error de verificación", "verificationErrorDesc": "No se pudo verificar tu cuenta. Por favor, intenta nuevamente.", "registrationError": "Error de registro", "registrationErrorDesc": "No se pudo crear la cuenta. Por favor, intenta nuevamente.", "authError": "Error de autenticación", "unexpectedError": "Error inesperado durante el registro", "tryAgain": "Ha ocurrido un error durante el registro. Por favor, intenta nuevamente."}, "success": {"welcome": "¡Bienvenido!", "loginSuccess": "Has iniciado sesión correctamente.", "userInitialized": "Usuario inicializado correctamente:"}, "validation": {"invalidEmail": "<PERSON><PERSON>", "passwordLength": "La contraseña debe tener al menos 6 caracteres", "nameRequired": "El nombre es requerido", "passwordsDoNotMatch": "Las contraseñas no coinciden"}, "confirmSignup": {"title": "Verifica tu email", "sentConfirmation": "Hemos enviado un enlace de confirmación a:", "checkInbox": "Por favor, revisa tu bandeja de entrada y sigue las instrucciones para activar tu cuenta.", "backToLogin": "Volver al inicio de sesión"}}, "landing": {"hero": {"badge": "¡Hazte con todos!", "title": "Gestiona tu Colección de Cartas Pokémon", "subtitle": "Ra<PERSON><PERSON>, organiza y muestra tu colección de Pokémon TCG con nuestras potentes herramientas de gestión.", "cta": "<PERSON><PERSON><PERSON>", "cardAlt1": "Carta de Charizard", "cardAlt2": "Carta de <PERSON>", "cardAlt3": "<PERSON><PERSON>t<PERSON>", "cardAlt4": "Carta de Blastoise"}, "features": {"badge": "Características", "title": "Todo lo que Necesitas para tu Colección", "subtitle": "PokéCollector te proporciona todas las herramientas necesarias para gestionar tu colección de cartas Pokémon.", "search": {"title": "Búsqueda de Cartas", "description": "Busca y filtra entre miles de cartas Pokémon por set, tipo, rareza y más."}, "collection": {"title": "Gestión de Colección", "description": "Añade cartas a tu colección, controla cantidades y organízalas como prefieras."}, "wishlist": {"title": "Lista de Deseos", "description": "Mantén un registro de las cartas que deseas adquirir con nuestra función de lista de deseos."}, "customCollections": {"title": "Colecciones Personalizadas", "description": "Crea y gestiona múltiples colecciones personalizadas para diferentes sets o temas."}, "filtering": {"title": "<PERSON><PERSON><PERSON>", "description": "Filtra tu colección por set, tipo, rareza y más para encontrar exactamente lo que buscas."}, "realtime": {"title": "Actualizaciones en Tiempo Real", "description": "Tu colección se sincroniza en todos tus dispositivos con actualizaciones en tiempo real."}}, "cta": {"title": "¿Listo para Comenzar tu Colección?", "subtitle": "Únete a miles de coleccionistas de cartas Pokémon que ya están usando PokéCollector para gestionar sus colecciones.", "pricingButton": "Ver Planes de Precios", "exploreButton": "<PERSON><PERSON><PERSON><PERSON>"}}, "errors": {"generic": "Ha ocurrido un error", "notFound": "No encontrado", "unauthorized": "No autorizado", "forbidden": "Prohibido", "validation": "Error de validación", "network": "Error de red", "server": "Error del servidor", "tooManyAttempts": "<PERSON><PERSON><PERSON><PERSON>", "waitBeforeRetry": "Por favor, espera antes de intentarlo de nuevo"}, "onboarding": {"title": "Guía de inicio - PokéCollector", "description": "Guía interactiva para comenzar a usar PokéCollector y gestionar tu colección de cartas Pokémon", "welcome": "¡Bienvenido a PokéCollector!", "intro": "Tu compañero perfecto para gestionar tu colección de cartas Pokémon. Aquí tienes una breve guía de las principales funcionalidades:", "startButton": "Comenzar a Coleccionar", "steps": {"search": {"title": "Buscar Cartas", "description": "Explora miles de cartas Pokémon usando nuestras herramientas de búsqueda y filtrado."}, "collection": {"title": "Construye tu Colección", "description": "Añade cartas a tu colección con detalles como cantidad, condición y notas."}, "wishlist": {"title": "Crear una Lista de Deseos", "description": "Mantén un seguimiento de las cartas que deseas añadir a tu colección en el futuro."}}}, "footer": {"description": "La herramienta definitiva para gestionar tu colección de cartas Pokémon TCG, para coleccionistas de todos los niveles.", "features": {"title": "Características", "cardSearch": "Búsqueda de Cartas", "collectionManagement": "Gestión de Colección", "wishlist": "Lista de Deseos"}, "resources": {"title": "Recursos", "guides": "<PERSON><PERSON><PERSON>", "rules": "Reg<PERSON>", "blog": "Blog", "tutorials": "Tu<PERSON>les"}, "company": {"title": "Compañía", "about": "Acerca de", "contact": "Contacto", "privacy": "Política de Privacidad", "terms": "Términos de Servicio"}, "copyright": "Todos los derechos reservados.", "disclaimer": "Pokémon y los nombres de personajes de Pokémon son marcas registradas de Nintendo. Este sitio no está afiliado con Nintendo o The Pokémon Company."}, "sidebar": {"openMenu": "<PERSON><PERSON><PERSON>", "closeMenu": "<PERSON><PERSON><PERSON>"}, "pokemonTypes": {"colorless": "Incoloro", "darkness": "Oscuridad", "dragon": "Dragón", "fairy": "<PERSON><PERSON>", "fighting": "<PERSON><PERSON>", "fire": "Fuego", "grass": "Planta", "lightning": "Rayo", "metal": "Metal", "psychic": "Psíquico", "water": "Agua"}, "cardSupertypes": {"energy": "Energía", "pokemon": "Pokémon", "pokémon": "Pokémon", "trainer": "Entrenador"}, "cardSubtypes": {"acespec": "ACE SPEC", "ancient": "Antigua", "baby": "<PERSON><PERSON><PERSON>", "basic": "Básico", "break": "BREAK", "eternamax": "Eternamax", "ex": "EX", "fusion": "Fusión", "fusionStrike": "<PERSON><PERSON><PERSON>", "future": "<PERSON><PERSON><PERSON>", "goldenrodGameCorner": "Rincón de Juegos de Ciudad Trigal", "gx": "GX", "item": "<PERSON><PERSON><PERSON><PERSON>", "legend": "Leyenda", "levelup": "Nivel-Superior", "mega": "MEGA", "pokemontool": "Herramienta Pokémon", "pokemontoolF": "Herramienta Pokémon F", "prime": "Prime", "prismstar": "Prisma Estrella", "radiant": "<PERSON><PERSON><PERSON>", "rapidstrike": "<PERSON><PERSON><PERSON>", "restored": "<PERSON><PERSON><PERSON>", "rocketsSecretMachine": "Máquina Secreta del Team Rocket", "singlestrike": "<PERSON><PERSON><PERSON>", "sp": "SP", "special": "Especial", "stadium": "Estadio", "stage1": "Fase 1", "stage2": "Fase 2", "star": "Estrella", "supporter": "Apoyo", "tagteam": "Equipo TAG", "teamplasma": "Equipo Plasma", "technicalmachine": "Máquina Técnica", "tera": "<PERSON><PERSON>", "tool": "Herramienta", "ultrabeast": "Ultraente", "v": "V", "vmax": "VMAX", "vstar": "VSTAR", "vunion": "V-UNION", "rapid": "<PERSON><PERSON><PERSON>", "single": "<PERSON><PERSON><PERSON>", "ace": "ACE SPEC"}, "cardConditions": {"mint": "Perfecta", "nearmint": "<PERSON>asi Perfecta", "excellent": "Excelente", "good": "Buena", "lightplayed": "Poco Usada", "played": "Usada", "poor": "Deteriorada"}, "cardFinishes": {"regular": "Regular", "foil": "Foil", "holo": "Holo", "reverseHolo": "<PERSON><PERSON>"}, "cardEditions": {"first": "Primera Edición", "unlimited": "Ilimitada", "limited": "Limitada"}, "cardRarities": {"Common": "Común", "Uncommon": "Poco común", "Rare": "<PERSON><PERSON>", "RareHolo": "<PERSON><PERSON>", "RareUltra": "<PERSON><PERSON>", "RareSecret": "<PERSON><PERSON>", "RareRainbow": "<PERSON><PERSON>", "RareHoloEX": "<PERSON><PERSON>", "RareHoloV": "<PERSON><PERSON>", "RareHoloVMAX": "Rara Holo VMAX", "RareHoloVSTAR": "<PERSON>ra <PERSON> VSTAR", "RarePrism": "<PERSON><PERSON>", "RarePrismStar": "<PERSON><PERSON>", "RareShinyGX": "<PERSON><PERSON> GX", "AmazingRare": "<PERSON><PERSON>", "ClassicCollection": "Colección Clásica", "Promo": "Promocional", "IllustratorRare": "<PERSON><PERSON>", "IllustrationRare": "Ilustración Rara", "ShinyRare": "<PERSON><PERSON>", "TrainerGalleryRareHolo": "Galería Entrenador <PERSON>", "RareHoloLV.X": "Rara Holo LV.X", "UltraRare": "Ultra Rara", "DoubleRare": "<PERSON><PERSON>", "RareHoloGX": "<PERSON>ra <PERSON>", "ShinyUltraRare": "Ultra Ra<PERSON>", "SpecialIllustrationRare": "Ilustración Rara Especial", "RareHoloStar": "<PERSON><PERSON>", "RareShiny": "<PERSON><PERSON>", "ACESPECRare": "Rara ACE SPEC", "HyperRare": "<PERSON><PERSON>", "LEGEND": "LEYENDA", "RadiantRare": "<PERSON><PERSON>", "RareACE": "Rara ACE", "RareBREAK": "<PERSON>ra <PERSON>", "RarePrime": "<PERSON><PERSON>", "RareShining": "<PERSON><PERSON>", "common": "Común", "uncommon": "Poco común", "rare": "<PERSON><PERSON>", "rareholo": "<PERSON><PERSON>", "rareultra": "<PERSON><PERSON>", "raresecret": "<PERSON><PERSON>", "rarerainbow": "<PERSON><PERSON>", "rareholoex": "<PERSON><PERSON>", "rareholov": "<PERSON><PERSON>", "rareholovmax": "Rara Holo VMAX", "rareholovstar": "<PERSON>ra <PERSON> VSTAR", "rareprism": "<PERSON><PERSON>", "rareprismstar": "<PERSON><PERSON>", "rareshinygx": "<PERSON><PERSON> GX", "amazingrare": "<PERSON><PERSON>", "classiccollection": "Colección Clásica", "promo": "Promocional", "illustratorrare": "<PERSON><PERSON>", "illustrationrare": "Ilustración Rara", "shinyrare": "<PERSON><PERSON>", "trainergalleryrareholo": "Galería Entrenador <PERSON>", "rarehololvx": "Rara Holo LV.X", "ultrarare": "Ultra Rara", "doublerare": "<PERSON><PERSON>", "rarehologx": "<PERSON>ra <PERSON>", "shinyultrarare": "Ultra Ra<PERSON>", "specialillustrationrare": "Ilustración Rara Especial", "rareholostar": "<PERSON><PERSON>", "rareshiny": "<PERSON><PERSON>", "acespecrare": "Rara ACE SPEC", "hyperrare": "<PERSON><PERSON>", "legend": "LEYENDA", "radiantrare": "<PERSON><PERSON>", "rareace": "Rara ACE", "rarebreak": "<PERSON>ra <PERSON>", "rareprime": "<PERSON><PERSON>", "rareshining": "<PERSON><PERSON>"}, "checkout": {"confirmSubscription": "Confirmar suscrip<PERSON>", "selectedPlan": "Plan seleccionado:", "price": "Precio:", "perMonth": "/mes", "continueToPayment": "Continuar al pago", "processingPayment": "Procesando pago...", "error": "Error al procesar el pago", "errorDescription": "Error al conectar con el servicio de pagos. Por favor, intenta nuevamente.", "noCheckoutUrl": "No se recibió la URL de checkout"}, "toasts": {"loginSuccess": "Inicio de sesión exitoso", "logoutSuccess": "Cierre de sesión exitoso", "registerSuccess": "<PERSON><PERSON>oso", "passwordResetEmailSent": "Correo de restablecimiento de contraseña enviado", "passwordChanged": "Contraseña cambiada exitosamente", "nameUpdated": "Nombre actualizado exitosamente", "accountDeleted": "Cuenta eliminada exitosamente", "collectionCreated": "Colección creada exitosamente", "collectionUpdated": "Colección actualizada exitosamente", "collectionDeleted": "Colección eliminada exitosamente", "cardAdded": "Carta añadida a la colección", "addingCard": "Añadiendo carta a la colección...", "cardRemoved": "Carta eliminada de la colección", "cardUpdated": "Carta actualizada en la colección", "wishlistAdded": "Carta añadida a la lista de deseos", "wishlistRemoved": "Carta eliminada de la lista de deseos", "subscriptionUpdated": "Suscripción actualizada exitosamente", "subscriptionCancelled": "Suscripción cancelada exitosamente", "error": "Ha ocurrido un error", "networkError": "Error de red. Por favor, verifica tu conexión", "unauthorized": "No autorizado. Por favor, inicia sesión nuevamente", "sessionExpired": "Tu sesión ha expirado. Por favor, inicia sesión nuevamente", "success": "¡Éxito!"}, "pagination": {"showing": "Mostrando", "of": "de", "results": "resultados", "previous": "Anterior", "next": "Siguient<PERSON>", "morePages": "<PERSON>ás páginas", "goToPage": "Ir a la página", "goToPreviousPage": "Ir a la página anterior", "goToNextPage": "Ir a la página siguiente"}, "admin": {"title": "Panel de Administración", "loading": "Cargando panel de administración...", "error": "Error al cargar el panel de administración", "unauthorized": "No tienes permisos para acceder al panel de administración", "loggedInAs": "Conectado como", "users": "Usuarios", "subscriptions": "Suscripciones", "auditLogs": {"title": "Registros de Auditoría", "description": "Historial completo de acciones administrativas", "loading": "Cargando registros de auditoría...", "noLogs": "No se encontraron registros de auditoría", "totalLogs": "Total de registros", "filters": {"title": "Filtros y Búsqueda", "adminUser": "<PERSON><PERSON>rio <PERSON>mini<PERSON>", "targetUser": "Usuario Objetivo", "action": "Acción", "entityType": "Tipo de Entidad", "dateRange": "<PERSON><PERSON>", "searchPlaceholder": "Buscar por email...", "allAdmins": "Todos los Administradores", "allTargets": "Todos los Objetivos", "allActions": "Todas las Acciones", "allEntities": "Todas las Entidades", "dateFrom": "<PERSON><PERSON>", "dateTo": "<PERSON><PERSON>", "clearFilters": "Limpiar <PERSON>", "applyFilters": "Aplicar Filtros"}, "table": {"timestamp": "Marc<PERSON> T<PERSON>mpo", "admin": "Administrador", "action": "Acción", "entity": "Entidad", "target": "Objetivo", "details": "Detalles", "viewDetails": "<PERSON><PERSON>", "hideDetails": "<PERSON><PERSON><PERSON><PERSON>"}, "actions": {"CREATE": "<PERSON><PERSON><PERSON>", "UPDATE": "Actualizar", "DELETE": "Eliminar", "LOGIN": "<PERSON><PERSON><PERSON>", "LOGOUT": "<PERSON><PERSON><PERSON>", "PLAN_CHANGE": "Cambio de Plan", "STATUS_CHANGE": "Cambio de Estado", "OVERRIDE_CREATE": "<PERSON><PERSON><PERSON>", "OVERRIDE_DEACTIVATE": "Desactivar Anulación", "SYNC_STRIPE": "Sincronizar Stripe", "CANCEL_SUBSCRIPTION": "Cancelar Suscripción"}, "entities": {"user": "Usuario", "subscription": "Suscripción", "collection": "Colección", "wishlist": "Lista de Deseos", "override": "Anulación", "admin": "Administrador"}, "details": {"title": "Detalles de la Acción", "adminUser": "<PERSON><PERSON>rio <PERSON>mini<PERSON>", "targetUser": "Usuario Objetivo", "timestamp": "Marc<PERSON> T<PERSON>mpo", "ipAddress": "Dirección IP", "userAgent": "Agente de Usuario", "metadata": "Metadatos", "changes": "Cambios", "oldValues": "Valores Anteriores", "newValues": "Valores Nuevos", "noChanges": "No se registraron cambios", "noMetadata": "No hay metadatos disponibles", "unknown": "Desconocido"}, "export": {"button": "Exportar Registros", "csv": "Exportar a CSV", "exporting": "Exportando...", "success": "Registros exportados exitosamente", "error": "Error al exportar registros"}, "errors": {"loadFailed": "Error al cargar registros de auditoría", "exportFailed": "Error al exportar registros de auditoría", "filterError": "Error al aplicar filtros"}}, "settings": {"title": "Configuración de Administración", "description": "Configuración del sistema y herramientas administrativas", "loading": "Cargando configuración...", "systemConfig": {"title": "Configuración del Sistema", "description": "Configuración de la aplicación y del entorno", "environment": "Entorno", "version": "Versión", "buildDate": "Fecha de Compilación", "nodeVersion": "Versión de Node", "databaseUrl": "URL de Base de Datos", "apiUrl": "URL de API", "frontendUrl": "URL de Frontend", "features": "Banderas de Características", "maintenance": "<PERSON><PERSON> Mantenimiento", "enabled": "Habilitado", "disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unknown": "Desconocido"}, "adminUsers": {"title": "Usuarios Administradores", "description": "Gestionar privilegios y acceso de administradores", "totalAdmins": "Total de Administradores", "addAdmin": "<PERSON><PERSON><PERSON><PERSON>", "removeAdmin": "Remover Administrador", "searchAdmins": "Buscar usuarios administradores...", "grantAdmin": "O<PERSON>gar Acceso de Administrador", "revokeAdmin": "Revocar Acceso de Administrador", "confirmGrant": "¿Estás seguro de que quieres otorgar privilegios de administrador a este usuario?", "confirmRevoke": "¿Estás seguro de que quieres revocar los privilegios de administrador de este usuario?", "adminGranted": "Privilegios de administrador otorgados exitosamente", "adminRevoked": "Privilegios de administrador revocados exitosamente", "cannotRevokeYourself": "No puedes revocar tus propios privilegios de administrador", "lastLogin": "Último Acceso", "loginCount": "Conteo de Accesos", "status": "Estado", "actions": "Acciones"}, "security": {"title": "Configuración de Seguridad", "description": "Configuración de autenticación y seguridad", "sessionTimeout": "Tiempo de Espera de Sesión", "maxLoginAttempts": "Máximo de Intentos de Acceso", "passwordPolicy": "Política de Contraseñas", "twoFactorAuth": "Autenticación de Dos Factores", "ipWhitelist": "Lista Blanca de IP", "rateLimiting": "Limitación de Velocidad", "auditLogging": "Registro de Auditoría", "minutes": "minutos", "attempts": "<PERSON><PERSON>", "active": "Activo", "inactive": "Inactivo"}, "database": {"title": "Gestión de Base de Datos", "description": "Estadísticas de base de datos y herramientas de mantenimiento", "connectionStatus": "Estado de Conexión", "totalTables": "Total de Tablas", "totalRecords": "Total de Registros", "databaseSize": "Tamaño de Base de Datos", "lastBackup": "<PERSON><PERSON><PERSON>", "connected": "Conectado", "disconnected": "Desconectado", "tables": "Tablas", "records": "Registros", "size": "<PERSON><PERSON><PERSON>", "runMaintenance": "<PERSON><PERSON><PERSON><PERSON>", "createBackup": "<PERSON><PERSON><PERSON>", "viewLogs": "Ver Registros", "maintenanceRunning": "<PERSON>je<PERSON><PERSON><PERSON> man<PERSON>o...", "maintenanceComplete": "Mantenimiento completado exitosamente", "backupCreated": "<PERSON><PERSON><PERSON> creado exitosamente"}, "apis": {"title": "Configuración de API", "description": "Configuración y monitoreo de APIs externas", "pokemonTcgApi": "API de Pokémon TCG", "stripeApi": "API de Stripe", "supabaseApi": "API de Supabase", "status": "Estado", "lastCheck": "Última Verificación", "responseTime": "Tiempo de Respuesta", "requestCount": "Conteo de Solicitudes", "errorRate": "Tasa de <PERSON>", "operational": "Operacional", "degraded": "Degradado", "down": "Caído", "testConnection": "Probar Conexión", "testing": "Probando...", "testSuccessful": "Prueba de conexión exitosa", "testFailed": "Prueba de conexión falló"}, "maintenance": {"title": "Mantenimiento del Sistema", "description": "Herramientas de mantenimiento y limpieza del sistema", "clearCache": "<PERSON><PERSON><PERSON>", "cleanupLogs": "Limpiar Registros", "optimizeDatabase": "Optimizar Base de Datos", "rebuildIndexes": "Reconstruir Índices", "cacheCleared": "Caché limpiado exitosamente", "logsCleanedUp": "Registros limpiados exitosamente", "databaseOptimized": "Base de datos optimizada exitosamente", "indexesRebuilt": "Índices reconstruidos exitosamente", "lastCacheClean": "Última Limpieza de Caché", "lastLogCleanup": "Última Limpieza de Registros", "lastOptimization": "Última Optimización", "systemHealth": "Salud del Sistema", "healthy": "Saludable", "warning": "Advertencia", "critical": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "errors": {"loadFailed": "Error al cargar configuración", "updateFailed": "Error al actualizar configuración", "maintenanceFailed": "Operación de mantenimiento falló", "testFailed": "Prueba de conexión falló", "unauthorized": "No autorizado para realizar esta acción"}, "success": {"settingsUpdated": "Configuración actualizada exitosamente", "operationComplete": "Operación completada exitosamente"}}, "userManagement": "Gestión de Usuarios", "subscriptionManagement": "Gestión de Suscripciones", "userManagementPlaceholder": "El componente de Gestión de Usuarios se implementará aquí", "subscriptionManagementPlaceholder": "El componente de Gestión de Suscripciones se implementará aquí", "auditLogsDescription": "Rastrea y monitorea todas las acciones administrativas para cumplimiento y seguridad", "settingsDescription": "Configura ajustes del sistema y gestiona funciones administrativas", "loadingUsers": "Cargando usuarios...", "totalUsers": "Total de usuarios", "filtersAndSearch": "Filtros y Búsqueda", "searchUsers": "Buscar usuarios...", "adminStatus": "Estado de Administrador", "allUsers": "Todos los Usuarios", "adminsOnly": "Solo Administradores", "regularUsers": "Usuarios Regulares", "activeStatus": "Estado Activo", "allStatuses": "Todos los Estados", "activeUsers": "Usuarios Activos", "inactiveUsers": "Usuarios Inactivos", "planType": "Tipo de Plan", "allPlans": "Todos los Planes", "user": "Usuario", "plan": "Plan", "status": "Estado", "lastLogin": "Último Acceso", "statistics": "Estadísticas", "actions": "Acciones", "admin": "Administrador", "inactive": "Inactivo", "logins": "accesos", "cards": "cartas", "collections": "colecciones", "wishlist": "lista de deseos", "never": "Nunca", "userDetails": "Detalles del Usuario", "basicInfo": "Información Básica", "email": "Correo Electrónico", "fullName": "Nombre Completo", "notProvided": "No proporcionado", "registrationDate": "<PERSON><PERSON>", "loginCount": "Número de Accesos", "regularUser": "Usuario Regular", "active": "Activo", "subscriptionInfo": "Información de Suscripción", "stripeSubscription": "Suscripción de Stripe", "notConnected": "No conectado", "noSubscription": "No se encontró suscripción", "userStatistics": "Estadísticas del Usuario", "totalCards": "Total de Cartas", "totalCollections": "Total de Colecciones", "totalWishlistItems": "Total de Elementos en Lista de Deseos", "noStatistics": "No hay estadísticas disponibles", "editUser": "<PERSON><PERSON>", "editUserDescription": "Realiza cambios en la información y permisos del usuario.", "enterFullName": "Ingresa el nombre completo", "adminPrivileges": "Privilegios de Administrador", "activeAccount": "Cuenta Activa", "notes": "Notas", "enterNotes": "Ingresa notas de administrador...", "deleteUser": "Eliminar Usua<PERSON>", "deleteUserWarning": "Esta acción no se puede deshacer. Esto eliminará permanentemente la cuenta del usuario y todos los datos asociados.", "deleteConfirmation": "Escribe 'DELETE' para confirmar esta acción", "success": "Éxito", "userUpdated": "Usuario actualizado exitosamente", "userDeleted": "Usuario eliminado exitosamente", "usersLoadError": "Error al cargar usuarios", "userDetailsError": "Error al cargar detalles del usuario", "userUpdateError": "<PERSON><PERSON>r al actualizar usuario", "userDeleteError": "Error al eliminar usuario", "andMore": "Y {{count}} más...", "subscriptionManagementDescription": "Gestionar suscripciones de usuarios, planes y facturación", "userSearch": "Búsqueda de Usuario", "enterUserEmail": "Ingresa el correo del usuario...", "search": "Buscar", "userNotFound": "Usuario No Encontrado", "userNotFoundDescription": "No se encontró ningún usuario con esa dirección de correo", "searchError": "Error al buscar usuario", "subscriptionDetails": "Detalles de Suscripción", "userInfo": "Información del Usuario", "currentSubscription": "Suscripción Actual", "changePlan": "Cambiar Plan", "changeStatus": "<PERSON><PERSON><PERSON>", "syncStripe": "Sincronizar Stripe", "isActive": "Está Activo", "currentPeriodStart": "Inicio del Período Actual", "currentPeriodEnd": "Fin del Período Actual", "cancelAtPeriodEnd": "Cancelar al Final del Período", "yes": "Sí", "no": "No", "stripeSubscriptionId": "ID de Suscripción de Stripe", "stripeCustomerId": "ID de Cliente de Stripe", "createSubscription": "<PERSON><PERSON><PERSON>", "activeOverrides": "Anulaciones Activas", "createOverride": "<PERSON><PERSON><PERSON>", "reason": "Razón", "expires": "Expira", "noOverrides": "No hay anulaciones activas", "dangerZone": "Zona de Peligro", "cancelSubscription": "Cancelar Suscripción", "cancelSubscriptionDescription": "Cancelar permanentemente la suscripción del usuario", "cancel": "<PERSON><PERSON><PERSON>", "changePlanDescription": "Cambiar el plan de suscripción del usuario. Esta acción será registrada para fines de auditoría.", "newPlan": "Nuevo Plan", "selectPlan": "Selecciona un plan", "enterReason": "Ingresa la razón del cambio de plan...", "planChanged": "Plan cambiado exitosamente", "planChangeError": "Error al cambiar el plan", "changeStatusDescription": "Cambiar el estado de la suscripción. Esto afectará el acceso del usuario a las funciones.", "newStatus": "Nuevo Estado", "selectStatus": "Selecciona un estado", "paused": "<PERSON><PERSON><PERSON>", "canceled": "Cancelado", "statusChanged": "Estado de suscripción cambiado exitosamente", "statusChangeError": "Error al cambiar el estado de la suscripción", "syncStripeDescription": "Sincronizar datos de suscripción con Stripe. Esto obtendrá la información más reciente de Stripe y actualizará la base de datos local.", "syncWarning": "Esto sobrescribirá los datos de suscripción locales con información de Stripe. Asegúrate de que esto es lo que quieres hacer.", "syncNow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "syncCompleted": "Sincronización de Stripe completada", "syncError": "Error al sincronizar con Stripe", "cancelSubscriptionWarning": "Esto cancelará la suscripción del usuario. Elige si cancelar inmediatamente o al final del período de facturación actual.", "cancelImmediately": "Cancelar inmediatamente (el usuario pierde acceso ahora)", "enterCancelReason": "Ingresa la razón de la cancelación...", "cancelConfirmation": "Esta acción no se puede deshacer. El usuario perderá acceso a las funciones premium.", "subscriptionCancelled": "Suscripción cancelada exitosamente", "cancelError": "Error al cancelar la suscripción", "createOverrideDescription": "Crear una anulación temporal para límites o funciones de suscripción.", "overrideType": "Tipo de Anulación", "selectOverrideType": "Selecciona el tipo de anulación", "planChange": "Cambio de Plan", "limitOverride": "Anulación de Límite", "featureAccess": "Acceso a Función", "billingOverride": "Anulación de Facturación", "originalValue": "Valor Original", "enterOriginalValue": "Ingresa el valor original...", "overrideValue": "Valor de Anulación", "enterOverrideValue": "Ingresa el valor de anulación...", "expirationDate": "Fecha de Expiración (Opcional)", "enterOverrideReason": "Ingresa la razón de la anulación...", "overrideCreated": "Anulación creada exitosamente", "overrideError": "Error al crear la anulación", "fillAllFields": "Por favor completa todos los campos requeridos"}}