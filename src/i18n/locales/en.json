{"common": {"processing": "Processing...", "loading": "Loading...", "search": "Search", "back": "Back", "edit": "Edit", "delete": "Delete", "save": "Save", "cancel": "Cancel", "confirm": "Confirm", "add": "Add", "remove": "Remove", "create": "Create", "update": "Update", "close": "Close", "yes": "Yes", "no": "No", "success": "Success", "error": "Error", "warning": "Warning", "info": "Information", "understood": "Understood", "tryAgainLater": "Please try again later", "openMenu": "Open menu", "verifying": "Verifying...", "unlimited": "Unlimited", "unknownCard": "Who's that <PERSON>?", "unknownSet": "Unknown Set", "cardUnavailable": "Card data unavailable", "cardUnavailableExplanation": "This card may be temporarily unavailable or may not exist in our database. You can still add it to your collection or wishlist.", "errorLoadingCard": "Error loading card data"}, "cookies": {"title": "<PERSON><PERSON>", "description": "We use cookies to enhance your browsing experience, analyze site traffic, and personalize content. By clicking 'Accept All', you consent to our use of cookies.", "learnMore": "Learn more about cookies", "acceptAll": "Accept All", "rejectAll": "Reject All", "savePreferences": "Save Preferences", "preferences": {"title": "Cookie Preferences", "description": "Manage your cookie preferences. You can enable or disable different types of cookies below.", "necessary": {"title": "Necessary Cookies", "description": "These cookies are essential for the website to function properly.", "details": "Necessary cookies help make a website usable by enabling basic functions like page navigation and access to secure areas of the website. The website cannot function properly without these cookies.", "services": "We use Supabase for user authentication and Stripe for payment processing. These services use essential cookies to provide their functionality."}, "analytics": {"title": "Analytics Cookies", "description": "These cookies help us understand how visitors interact with our website.", "details": "Analytics cookies help website owners understand how visitors interact with websites by collecting and reporting information anonymously. We use Google Analytics to collect information about how users use our site."}, "details": {"title": "Detailed Cookie Information", "description": "Below you can find more information about the specific cookies we use."}}}, "cookiePolicy": {"title": "<PERSON><PERSON>", "introduction": "This Cookie Policy explains how PokéCollector uses cookies and similar technologies to recognize you when you visit our website. It explains what these technologies are and why we use them, as well as your rights to control our use of them.", "whatAreCookies": {"title": "What Are Cookies?", "description": "Cookies are small data files that are placed on your computer or mobile device when you visit a website. Cookies are widely used by website owners in order to make their websites work, or to work more efficiently, as well as to provide reporting information. Cookies set by the website owner (in this case, PokéCollector) are called 'first-party cookies'. Cookies set by parties other than the website owner are called 'third-party cookies'. Third-party cookies enable third-party features or functionality to be provided on or through the website (e.g., advertising, interactive content, and analytics)."}, "typesOfCookies": {"title": "Types of Cookies We Use"}, "howWeUse": {"title": "How We Use Cookies", "description": "We use cookies for several reasons. Some cookies are required for technical reasons in order for our website to operate, and we refer to these as 'necessary' cookies. Other cookies also enable us to track and target the interests of our users to enhance the experience on our website. Third parties serve cookies through our website for analytics and other purposes."}, "googleAnalytics": {"title": "Google Analytics", "description": "We use Google Analytics to help us understand how our customers use the site. You can read more about how Google uses your Personal Information here: https://policies.google.com/privacy. You can opt-out of Google Analytics tracking by disabling analytics cookies in your cookie preferences."}, "stripe": {"title": "Stripe", "description": "We use <PERSON><PERSON> for payment processing. <PERSON><PERSON> uses cookies to enable the payment functionality, prevent fraud, and improve the security of our payment processing. These cookies are necessary for the proper functioning of the payment features."}, "supabase": {"title": "Supabase", "description": "We use Supabase for user authentication and data storage. Supabase uses cookies and local storage to maintain your authentication state and ensure you stay logged in. These cookies are necessary for the proper functioning of the authentication features."}, "managingCookies": {"title": "Managing Your Cookie Preferences", "description": "You can set or amend your cookie preferences at any time by using our cookie preference center. This allows you to reject non-essential cookies."}, "manageCookies": "Manage Cookie Preferences", "browserSettings": {"title": "Browser Controls", "description": "Most web browsers also allow some control of most cookies through the browser settings. To find out more about cookies, including how to see what cookies have been set, visit www.aboutcookies.org or www.allaboutcookies.org. Find out how to manage cookies on popular browsers: Google Chrome, Microsoft Edge, Mozilla Firefox, Microsoft Internet Explorer, Opera, Apple Safari."}, "updates": {"title": "Updates to This <PERSON>ie Policy", "description": "We may update this Cookie Policy from time to time in order to reflect, for example, changes to the cookies we use or for other operational, legal, or regulatory reasons. Please therefore revisit this Cookie Policy regularly to stay informed about our use of cookies and related technologies."}, "contact": {"title": "Contact Us", "description": "If you have any questions about our use of cookies or other technologies, please email <NAME_EMAIL>."}, "lastUpdated": "Last updated:"}, "privacyPolicy": {"title": "Privacy Policy", "introduction": "This Privacy Policy describes how your personal information is collected, used, and shared when you visit or make a purchase from PokéCollector. We respect your privacy and are committed to protecting your personal data.", "lastUpdated": "Last updated:", "informationWeCollect": {"title": "Information We Collect", "description": "When you visit the site, we automatically collect certain information about your device, including information about your web browser, IP address, time zone, and some of the cookies that are installed on your device."}, "personalInformation": {"title": "Personal Information", "description": "When you register for an account or subscribe to our service, we may ask you to provide the following types of personal information:", "items": {"email": "Email address", "name": "Name", "billingInfo": "Billing information (for paid subscriptions)"}}, "usageData": {"title": "Usage Data", "description": "We may also collect information about how you access and use our service:", "items": {"ipAddress": "IP address", "browserType": "Browser type and version", "pagesVisited": "Pages of our service that you visit", "timeSpent": "Time spent on those pages"}}, "howWeUseInformation": {"title": "How We Use Your Information", "description": "We use the information we collect about you to:", "items": {"provideService": "Provide, operate, and maintain our service", "improveService": "Improve and personalize your experience on our site", "communicate": "Communicate with you, including for customer service", "processPayments": "Process payments and prevent fraud"}}, "dataStorage": {"title": "Where Your Data is Stored", "description": "Your data is stored securely with our service providers:"}, "supabase": {"title": "Supabase", "description": "We use Supabase to store user data and authentication information. Supabase stores data in accordance with GDPR requirements and implements appropriate security measures to protect your personal information."}, "stripe": {"title": "Stripe", "description": "For payment processing, we use Stripe. When you provide your payment information, it is transmitted directly to <PERSON><PERSON> and is not stored on our servers. Stripe processes your payment information in accordance with their privacy policy."}, "cookies": {"title": "Cookies and Tracking", "description": "We use cookies and similar tracking technologies to track activity on our service and hold certain information. You can instruct your browser to refuse all cookies or to indicate when a cookie is being sent.", "linkText": "Learn more about our Cookie Policy"}, "dataSharing": {"title": "Sharing Your Personal Information", "description": "We share your Personal Information with third parties to help us use your Personal Information, as described above. We may also share your Personal Information in the following situations:", "items": {"serviceProviders": "With service providers who perform services for us", "legalRequirements": "If required by law or in response to valid requests by public authorities", "businessTransfers": "In connection with, or during negotiations of, any merger, sale of company assets, financing, or acquisition"}}, "dataRetention": {"title": "Data Retention", "description": "We will retain your Personal Information only for as long as is necessary for the purposes set out in this Privacy Policy. We will retain and use your Personal Information to the extent necessary to comply with our legal obligations, resolve disputes, and enforce our legal agreements and policies."}, "yourRights": {"title": "Your Rights Under GDPR", "description": "If you are a resident of the European Economic Area (EEA), you have certain data protection rights. You have the right to:", "items": {"access": "Access information we hold about you", "rectification": "Rectify any inaccurate or incomplete personal information", "erasure": "Request the deletion of your personal data", "restriction": "Restrict or object to the processing of your personal data", "dataPortability": "Request the transfer of your data to you or a third party", "objection": "Object to the processing of your personal data for direct marketing"}, "exerciseRights": "To exercise these rights, please contact us using the contact information provided below."}, "dataProtectionOfficer": {"title": "Data Protection Officer", "description": "We have appointed a data protection officer (DPO) who is responsible for overseeing questions in relation to this privacy policy. If you have any questions about this privacy policy, please contact our <NAME_EMAIL>."}, "changes": {"title": "Changes to This Privacy Policy", "description": "We may update our Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy on this page and updating the 'last updated' date."}, "contact": {"title": "Contact Us", "description": "If you have any questions about this Privacy Policy, please contact us."}}, "termsOfService": {"title": "Terms of Service", "introduction": "These Terms of Service govern your use of the PokéCollector website and service. By accessing or using our service, you agree to be bound by these Terms.", "lastUpdated": "Last updated:", "definitions": {"title": "Definitions", "items": {"service": {"term": "Service", "definition": "The PokéCollector website and platform operated by us."}, "user": {"term": "User", "definition": "Any individual who accesses or uses the Service."}, "account": {"term": "Account", "definition": "A unique account created for you to access our Service or parts of our Service."}, "subscription": {"term": "Subscription", "definition": "The recurring payment plan that grants access to premium features of the Service."}, "company": {"term": "Company", "definition": "Refers to PokéCollector, the operator of this Service."}}}, "acceptance": {"title": "Acceptance of Terms", "description": "By accessing or using our Service, you confirm that you have read, understood, and agree to be bound by these Terms of Service. If you do not agree with any part of these terms, you may not use our Service."}, "userAccounts": {"title": "User Accounts", "description": "When you create an account with us, you must provide information that is accurate, complete, and current at all times. Failure to do so constitutes a breach of the Terms, which may result in immediate termination of your account on our Service.", "items": {"accuracy": "You are responsible for maintaining the accuracy of your account information.", "security": "You are responsible for safeguarding the password used to access the Service.", "unauthorized": "You agree to notify us immediately of any unauthorized access to or use of your account.", "termination": "We reserve the right to terminate or suspend your account at our sole discretion, without notice."}}, "subscriptions": {"title": "Subscriptions", "description": "Some parts of the Service are available on a subscription basis. You will be billed in advance on a recurring basis, depending on the type of subscription plan you select.", "billing": "By subscribing to our Service, you authorize us to charge your payment method on a recurring basis according to the terms of your subscription plan.", "cancellation": "You can cancel your subscription at any time through your account settings. Your subscription will remain active until the end of the current billing period."}, "intellectualProperty": {"title": "Intellectual Property", "description": "The Service and its original content, features, and functionality are and will remain the exclusive property of PokéCollector and its licensors. The Service is protected by copyright, trademark, and other laws.", "userContent": "By submitting content to the Service, you grant us a worldwide, non-exclusive, royalty-free license to use, reproduce, modify, adapt, publish, translate, and distribute your content in any existing or future media."}, "prohibitedUses": {"title": "Prohibited Uses", "description": "You may use our Service only for lawful purposes and in accordance with these Terms. You agree not to use the Service:", "items": {"illegal": "In any way that violates any applicable national or international law or regulation.", "harmful": "To transmit, or procure the sending of, any advertising or promotional material, including any 'junk mail', 'chain letter', 'spam', or any other similar solicitation.", "impersonation": "To impersonate or attempt to impersonate the Company, a Company employee, another user, or any other person or entity.", "infringement": "To engage in any activity that infringes or misappropriates the intellectual property rights of others.", "automation": "To use any robot, spider, or other automatic device to access the Service for any purpose without our express written permission."}}, "disclaimer": {"title": "Disclaimer of Warranties", "description": "Your use of the Service is at your sole risk. The Service is provided on an 'AS IS' and 'AS AVAILABLE' basis. The Service is provided without warranties of any kind, whether express or implied."}, "limitation": {"title": "Limitation of Liability", "description": "In no event shall PokéCollector, nor its directors, employees, partners, agents, suppliers, or affiliates, be liable for any indirect, incidental, special, consequential or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses, resulting from your access to or use of or inability to access or use the Service."}, "indemnification": {"title": "Indemnification", "description": "You agree to defend, indemnify, and hold harmless PokéCollector and its licensors, service providers, employees, agents, officers, and directors from and against any claims, liabilities, damages, judgments, awards, losses, costs, expenses, or fees (including reasonable attorneys' fees) arising out of or relating to your violation of these Terms or your use of the Service."}, "termination": {"title": "Termination", "description": "We may terminate or suspend your account immediately, without prior notice or liability, for any reason whatsoever, including without limitation if you breach the Terms. Upon termination, your right to use the Service will immediately cease."}, "governing": {"title": "Governing Law", "description": "These Terms shall be governed and construed in accordance with the laws of Spain, without regard to its conflict of law provisions."}, "changes": {"title": "Changes to Terms", "description": "We reserve the right, at our sole discretion, to modify or replace these Terms at any time. If a revision is material, we will try to provide at least 30 days' notice prior to any new terms taking effect."}, "contact": {"title": "Contact Us", "description": "If you have any questions about these Terms, please contact us."}, "relatedPolicies": "Please also review our other policies:", "privacyPolicyLink": "Privacy Policy", "cookiePolicyLink": "<PERSON><PERSON>"}, "navigation": {"dashboard": "Dashboard", "collection": "Collection", "wishlist": "Wishlist", "search": "Search", "account": "Account", "pricing": "Pricing", "rules": "Rules", "subscription": "Subscription"}, "app": {"title": "PokéCollector", "subtitle": "Manage your Pokémon card collection", "description": "The best way to organize and manage your Pokémon card collection"}, "dashboard": {"title": "My Pokémon Collection", "subtitle": "Manage your Pokémon card collection"}, "collection": {"maxCards": "Maximum cards", "maxCollections": "Maximum collections", "cards": "Cards", "collections": "Collections", "loading": "Loading collection...", "searchInCollection": "Search cards in this collection", "noSearchResults": "No cards found matching your search.", "title": "My Collection", "myCollections": "My Collections", "default": "<PERSON><PERSON><PERSON>", "create": "Create Collection", "edit": "Edit Collection", "delete": "Delete Collection", "empty": "You don't have any collections yet", "emptyDescription": "Create your first collection to start saving your cards.", "createFirst": "Create My First Collection", "confirmDelete": "Are you sure you want to delete this collection?", "deleteWarning": "This action cannot be undone and will delete all cards in this collection.", "addCard": "Add to Collection", "selectCollection": "Select collection", "adding": "Adding...", "removeCard": "Remove Card", "cardAdded": "Card added to collection", "cardAddedDescription": "The card has been added to your collection", "cardUpdatedDescription": "The card has been updated in your collection", "addingCard": "Adding card to collection...", "cardAddedToDefault": "Card added to default collection", "cardRemoved": "Card removed from collection", "collectionCreated": "Collection created successfully", "collectionUpdated": "Collection updated successfully", "collectionDeleted": "Collection deleted successfully", "addToDefault": "Add to default collection", "collectionDetails": "Collection Details", "defaultName": "My Collection", "defaultDescription": "My Pokémon card collection", "updated": "Collection updated", "created": "Collection created", "deleted": "Collection deleted", "saveSuccess": "Collection \"{name}\" has been saved successfully.", "deleteSuccess": "The collection has been deleted.", "editDescription": "Modify your collection details", "createDescription": "Create a new collection to organize your cards", "nameLabel": "Collection Name", "namePlaceholder": "My Awesome Collection", "descriptionLabel": "Description (Optional)", "descriptionPlaceholder": "Describe your collection...", "setAsDefault": "Set as default collection", "quantityIncreased": "The quantity has been increased", "deleteConfirmTitle": "Delete Collection", "deleteConfirmDescription": "Are you sure you want to delete the collection \"{name}\"?", "errors": {"loadFailed": "Could not load collections. Please try again.", "createDefaultFailed": "Could not create default collection. Please try again.", "defaultExists": "A default collection already exists. Please unmark the current default collection before setting another one.", "saveFailed": "Could not save collection. Please try again.", "deleteFailed": "Could not delete collection. Please try again.", "setDefaultFailed": "Could not set default collection.", "noSelectedCollection": "No collection selected", "createFailed": "Could not create collection. Please try again.", "addFailed": "Could not add card to collection. Please try again."}, "confirmDeleteWithName": "Are you sure you want to delete the collection \"{name}\"? This action cannot be undone and all associated cards will be deleted.", "noDefaultCollection": "No default collection", "pleaseCreateDefault": "Please create a default collection or set an existing one as default.", "defaultCollectionNeeded": "To use the quick add feature, you need to have a default collection.", "setExistingAsDefault": "You can set one of your existing collections as default:", "noCollectionsYet": "You don't have any collections. Create a new one to get started.", "createNew": "Create New Collection"}, "wishlist": {"maxItems": "Maximum wishlist items", "title": "Wishlist", "empty": "Your wishlist is empty", "addCard": "Add to wishlist", "removeCard": "Remove from wishlist", "loading": "Loading wishlist...", "emptyDescription": "Add cards to your wishlist to keep track of the ones you want to get", "searchCards": "Search cards to add", "cardAdded": "Card added to wishlist", "addSuccess": "The card has been added to your wishlist", "cardRemoved": "Card removed", "removeSuccess": "The card has been removed from your wishlist", "removedAfterAdding": "The card has been removed from your wishlist after adding it to your collection", "removedFromWishlist": "Removed from wishlist", "cardRemovedDescription": "The card has been removed from your wishlist", "errors": {"loadFailed": "Could not load wishlist", "removeFailed": "Could not remove card from wishlist", "addFailed": "Could not add card to wishlist.", "idNotFound": "Wishlist ID not found"}, "alreadyInWishlist": "Already in Wishlist", "cardAlreadyExists": "This card is already in your wishlist."}, "search": {"cards": "Search Cards", "title": "Search Cards", "nameSearchPlaceholder": "Search by name", "searching": "Searching...", "searchCards": "Search", "sortBy": "Sort by", "sortNameAsc": "Name (A-Z)", "sortNameDesc": "Name (Z-A)", "sortNumberAsc": "Number (Low to High)", "sortNumberDesc": "Number (High to Low)", "description": "Search through thousands of cards and find the ones you need for your collection", "placeholder": "Search by name, number or set", "filters": "Filters", "noResults": "No results found", "clearFilters": "Clear filters", "applyFilters": "Apply filters", "searchResults": "Search results", "loadMore": "Load more", "errors": {"loadFailed": "Could not load search results", "filtersFailed": "Could not load filters. Please try again.", "setsFailed": "Error loading sets", "typesFailed": "Error loading types", "raritiesFailed": "Error loading rarities", "filterDataFailed": "Error loading filter data"}}, "filters": {"pokemonType": "Pokémon Type", "allTypes": "All types", "rarity": "<PERSON><PERSON>", "allRarities": "All rarities", "supertype": "Supertype", "allSupertypes": "All supertypes", "subtype": "Subtype", "allSubtypes": "All subtypes", "name": "Name", "set": "Set", "type": "Type"}, "card": {"details": "Card details", "name": "Name", "number": "Number", "set": "Set", "rarity": "<PERSON><PERSON>", "type": "Type", "supertype": "Supertype", "subtype": "Subtype", "condition": "Condition", "price": "Price", "quantity": "Quantity", "notes": "Notes", "selectCondition": "Select condition", "personalNotes": "Personal notes about this card", "addToCollection": "Add to collection", "addToWishlist": "Add to wishlist", "removeFromCollection": "Remove from collection", "removeFromWishlist": "Remove from wishlist", "abilities": "Abilities", "attacks": "Attacks", "rules": "Rules", "weaknesses": "Weaknesses", "resistances": "Resistances", "retreatCost": "Retreat Cost", "foil": "Foil", "firstEdition": "First Edition", "conditionNearMint": "Near Mint", "removed": "Card removed", "updated": "Card updated", "removeSuccess": "The card has been removed from your collection.", "updateSuccess": "The card has been updated.", "edit": "Edit card", "editDescription": "Modify the details of this card in your collection", "notesPlaceholder": "Add personal notes about this card (optional)", "errors": {"removeFailed": "Could not remove card. Please try again.", "updateFailed": "Could not update card. Please try again."}}, "limits": {"cards": "card(s) in your collection", "collections": "Collections", "wishlist": "cards in your wishlist", "elements": "elements", "limitReached": "{{type}} Limit Reached", "currentPlanLimit": "Your current plan ({{plan}}) has reached its limit. Upgrade your plan to get access to more {{type}} and other features.", "upgradePlan": "Upgrade Plan"}, "subscription": {"loading": "Loading subscription...", "planUsage": "Current Plan Usage", "upgradePlan": "Upgrade Plan", "statusActive": "Active", "statusInactive": "Inactive", "viewPlans": "View Plans", "title": "Subscription Management", "description": "Manage your plan and usage", "currentPlan": "Current plan", "cancelPlan": "Cancel plan", "renewalDate": "Renewal date", "paymentMethod": "Payment method", "updatePayment": "Update payment method", "billingHistory": "Billing history", "planDetails": "Plan details", "limitReached": "You have reached your plan limit", "limitReachedMessage": "You have reached the limit of {{limit}} {{type}} in your {{plan}} plan", "upgradeNow": "Upgrade now", "active": "Your subscription is active", "inactive": "You don't have an active subscription", "plan": "Plan", "status": "Status", "successTitle": "Congratulations, you are now a {{planName}} Pokémon!", "successDescription": "Your subscription has been processed successfully.", "verifying": "Verifying subscription...", "verifyingStatus": "Verifying subscription status...", "nextBilling": "Next billing", "goToDashboard": "Go to Dashboard", "yourSubscription": "Your Subscription", "currentPlanDetails": "Details of your current subscription plan", "currentPeriod": "Current Period", "validUntil": "Valid until {{date}}", "includedFeatures": "Included Features", "cancelSubscription": "Cancel Subscription", "cancelConfirmation": "Are you sure you want to cancel your subscription? You will lose access to premium features at the end of your current billing period.", "canceling": "Canceling...", "canceled": "Subscription Canceled", "canceledDescription": "Your subscription has been canceled successfully. You will have access to premium features until the end of your current billing period.", "statusCanceled": "Canceled", "noActiveSubscription": "No Active Subscription", "considerSubscribing": "Consider subscribing to a plan to access premium features.", "actions": {"addToWishlist": "add to your wishlist", "addToCollection": "add cards to your collection", "createCollections": "create new collections"}, "downgrade": {"confirmTitle": "Confirm Plan Change", "description": "You are about to change from {currentPlan} to {targetPlan}. This change will reduce your account limits.", "warning": "Important: If you exceed the limits of the new plan, you won't be able to add more items until you free up space.", "confirm": "Confirm Change"}, "requiredTitle": "Subscription Required", "welcomeMessage": "Welcome to PokéCollector!", "changePlan": "Change Subscription Plan", "month": "month", "processing": "Processing...", "upgradeTo": "Upgrade to {{plan}}", "prorationAmount": "Amount to pay for plan change: {{amount}}€", "planUpdated": "Plan updated", "planUpdatedSuccess": "Your plan has been successfully updated", "errors": {"changePlan": "Error changing plan", "changePlanLog": "Error changing plan:", "tryAgain": "There was an error processing your request. Please try again.", "noSubscriptionInfo": "No subscription information available", "noStripeId": "No Stripe subscription ID found", "cancelFailed": "Failed to cancel subscription. Please try again.", "validationFailed": "Error validating subscription limits"}, "upgradeExperience": "Upgrade Your Experience", "requiredDescription": "To access {{action}}, you need to upgrade your subscription plan.", "discoverPremium": "Discover all premium features of PokéCollector.", "features": {"unlimitedCollections": "Unlimited collections", "extendedWishlist": "Extended wishlist", "exclusiveFeatures": "Exclusive premium features"}, "later": "Later"}, "pricing": {"price": "Price", "free": "Free", "title": "Plans & Pricing", "subtitle": "These are our plans for you. Start with the free one and level up when you need it", "startFree": "Start Free", "startFreeButton": "Start Free", "popular": "Popular", "popularBadge": "Popular", "currentPlan": "Current plan", "alreadyActive": "You already have this plan active.", "updateError": "Could not update plan. Please try again.", "faqTitle": "Frequently Asked Questions", "currentPlanToast": {"title": "Current plan", "description": "You already have this plan active."}, "errorUpdatingPlan": "Could not update plan. Please try again.", "faq": {"freeplan": {"question": "What does the free plan include?", "answer": "The Apprentice plan includes all the basic features to start your collection: up to 50 cards, one collection, 10 cards in your wishlist, and basic search by name and type. It's perfect to get started and familiarize yourself with the platform."}, "changePlan": {"question": "Can I change my plan at any time?", "answer": "Yes, you can upgrade or change your plan at any time. If you upgrade to a higher plan, you'll have immediate access to all the new features. If you decide to downgrade, the change will apply at the end of the current billing period."}, "collections": {"question": "How does the collections system work?", "answer": "Each collection allows you to organize your Pokémon cards however you prefer. You can create collections by set, by type, by rarity, or any other criteria. Each card can include details such as its condition, personal notes, and acquisition date."}, "payment": {"question": "What payment methods do you accept?", "answer": "We accept all major credit and debit cards (Visa, Mastercard, American Express) and PayPal. All payments are securely processed through Stripe."}, "cancel": {"question": "Can I cancel my subscription at any time?", "answer": "Yes, you can cancel your subscription whenever you want. There are no long-term commitments. When you cancel, you'll maintain access to premium features until the end of the billing period."}, "data": {"question": "What happens to my data if I cancel my subscription?", "answer": "If you cancel a premium subscription and return to the free plan, your data will remain saved, but you'll only be able to access the limitations of the free plan. You can regain access to all your data by reactivating your subscription."}, "wishlist": {"question": "How does the wishlist work?", "answer": "The wishlist allows you to save cards you'd like to acquire in the future. You can add notes, set priorities, and receive notifications when we update information about those cards."}, "share": {"question": "Can I share my collection with other users?", "answer": "Yes, you can share your collection with other users via a public link. You control what information is visible to other collectors and can disable visibility at any time."}, "database": {"question": "How do you keep the card database updated?", "answer": "Our database is regularly updated with the latest Pokémon card releases. We work with official sources and recognized databases to ensure you have access to the most accurate and up-to-date information."}, "support": {"question": "Do you offer technical support?", "answer": "Yes, we offer email technical support for all users. Premium plan users have access to priority support with guaranteed response times."}}, "perMonth": "month", "selectPlan": "Select plan", "maestroFeatures": {"0": "Everything in Trainer", "1": "Unlimited cards", "2": "Unlimited collections", "3": "Unlimited wishlist", "4": "Priority Support"}}, "plans": {"aprendiz": "Apprentice", "entrenador": "Trainer", "maestro": "Master", "descriptions": {"aprendiz": "Free plan to get started", "entrenador": "For serious collectors", "maestro": "For professional collectors"}, "featuresList": {"aprendiz": ["Up to 50 cards", "1 collection", "10 cards in wishlist", "Card search by name and type"], "entrenador": ["Up to 500 cards", "5 collections", "50 cards in wishlist", "Advanced card search by name, type, subtype, rarity and more"], "maestro": ["Everything in Trainer", "Unlimited cards", "Unlimited collections", "Unlimited wishlist", "Priority Support"]}, "perMonth": "/month", "selectPlan": "Select plan", "currentPlan": "Current Plan", "startFree": "Start Free", "popular": "Popular", "alreadyActive": "You already have this plan active.", "updateError": "Could not update plan. Please try again."}, "account": {"title": "My Account", "profile": "Profile", "security": "Security", "deleteAccount": "Delete Account", "changePassword": "Change Password", "dangerZone": "Danger Zone", "dangerZoneDescription": "Actions in this section are irreversible and may result in data loss", "passwordResetConfirmation": "We'll send you an email with instructions to change your password. Do you want to continue?", "instructionsSent": "Instructions <PERSON><PERSON>", "passwordResetInstructions": "We've sent you an email with instructions to change your password. Please check your inbox.", "deleteAccountConfirmation": "Are you sure you want to delete your account? This action is irreversible and you will lose all your collections and data.", "deleteAccountDescription": "Once you delete your account, there is no going back. Please be certain.", "settings": {"title": "Account <PERSON><PERSON>", "profile": "Profile", "security": "Security & Privacy", "preferences": "Preferences"}, "updateProfile": "Update profile", "changeEmail": "Change email", "emailChanged": "Email changed successfully", "passwordChanged": "Password changed successfully", "profileUpdated": "Profile updated successfully", "noName": "No name", "nameUpdated": "Name updated", "nameUpdatedSuccess": "Your name has been updated successfully", "nameUpdateError": "Could not update name", "passwordResetEmailSent": "Email sent", "checkInbox": "Check your inbox to change your password", "passwordResetError": "Could not send recovery email", "noActiveSession": "No active session", "deleteError": "Error deleting account", "deleteAccountError": "Error deleting account", "statistics": "Statistics", "currentUsage": "Current account usage", "cardsInCollections": "Cards in collections", "collections": "Collections", "wishlist": "Wishlist items", "email": "Email", "name": "Name", "notSet": "Not set", "emailSent": "Email sent", "checkInboxPassword": "Check your inbox and follow the instructions to set a new password", "changePasswordDescription": "We'll send you an email with a link to change your password", "sendResetEmail": "Send reset email", "checkEmail": "Check your email", "passwordEmailSent": "We've sent an email to {{email}} with instructions to change your password", "permanentlyDelete": "Permanently delete", "accountDeleted": "Account deleted", "accountDeleteSuccess": "Your account has been successfully deleted", "dangerDescription": "Irreversible actions for your account", "errors": {"resetEmailFailed": "Failed to send reset email", "nameUpdateFailed": "Failed to update name", "deleteFailed": "Failed to delete account"}, "goodbye": "Goodbye!", "accountDeletedMessage": "Your account has been successfully deleted. We hope to see you again soon at PokéCollector.", "welcomeBackMessage": "Remember that you will always be welcome back in our collector community.", "backToHome": "Back to home"}, "auth": {"login": "Log in", "signup": "Sign up", "logout": "Log out", "email": "Email", "emailPlaceholder": "<EMAIL>", "password": "Password", "confirmPassword": "Confirm password", "forgotPassword": "Forgot your password?", "resetPassword": "Reset password", "confirmEmail": {"title": "Confirm your email", "description": "Your account has not been verified yet. We've sent a new confirmation email to:", "checkInbox": "Please check your inbox and spam folder."}, "alreadyHaveAccount": "Already have an account?", "dontHaveAccount": "Don't have an account?", "signupSuccess": "Registration successful. Please verify your email.", "loginSuccess": "Login successful", "logoutSuccess": "Logout successful", "resetPasswordSuccess": "Password reset successful", "emailConfirmSuccess": "Email confirmed successfully", "fullName": "Full name", "fullNamePlaceholder": "Your name", "createAccount": "Create Account", "verifyEmail": "Verify your Email", "verificationLinkSent": "We've sent a verification link to:", "checkInboxInstructions": "Please check your inbox and follow the instructions to complete your registration.", "openGmail": "Open Gmail", "loggingIn": "Logging in...", "accessingAccount": "Accessing your account...", "registering": "Registering user...", "verifyNewEmail": "Verify your new email", "confirmationLinkSent": "We've sent a confirmation link to:", "checkInboxForNewEmail": "Please check your inbox and follow the instructions to confirm your new email.", "backToAccount": "Back to my account", "dialog": {"title": "<PERSON><PERSON>", "description": "To manage your collection and wishlist, you need to have an account. It's free and only takes a minute! You'll be able to:", "benefits": {"collection": "Create and manage your card collection", "wishlist": "Save cards to your wishlist", "tracking": "Track your favorite cards"}}, "newPassword": "New Password", "passwordChanged": "Your password has been successfully updated", "processingReset": "Sending instructions...", "errors": {"passwordsDoNotMatch": "Passwords do not match", "invalidPassword": "Invalid password", "invalidCredentials": "Incorrect email or password", "loginError": "Login error", "initError": "Error initializing user", "accountInitError": "There was a problem initializing your account.", "loginProcessError": "Error during login process", "signupError": "Registration error", "verificationError": "Verification error", "verificationErrorDesc": "Could not verify your account. Please try again.", "registrationError": "Registration error", "registrationErrorDesc": "Could not create account. Please try again.", "authError": "Authentication error", "unexpectedError": "Unexpected error during registration", "tryAgain": "An error occurred during registration. Please try again."}, "success": {"welcome": "Welcome!", "loginSuccess": "You have successfully logged in.", "userInitialized": "User successfully initialized:"}, "validation": {"invalidEmail": "Invalid email", "passwordLength": "Password must be at least 6 characters", "nameRequired": "Name is required", "passwordsDoNotMatch": "Passwords do not match"}, "loginRequiredForWishlist": "You must be logged in to add cards to your wishlist.", "loginRequiredForCollection": "You must be logged in to add cards to your collection.", "confirmSignup": {"title": "Verify your email", "sentConfirmation": "We've sent a confirmation link to:", "checkInbox": "Please check your inbox and follow the instructions to activate your account.", "backToLogin": "Back to login"}, "recoverPassword": "Recover Password", "sendInstructions": "Send Instructions", "sendingInstructions": "Sending instructions...", "backToLogin": "Back to login", "tooManyAttempts": "Too many attempts", "waitBeforeRetry": "Please wait 30 minutes before trying again", "emailSent": "Email sent", "checkInboxForReset": "Check your inbox to reset your password.", "errorTryAgain": "An error occurred. Please try again.", "instructionsSentTo": "We've sent instructions to:", "checkInboxAndFollow": "Check your inbox and follow the instructions to reset your password."}, "landing": {"hero": {"badge": "Gotta catch 'em all!", "title": "Manage your Pokémon Card Collection", "subtitle": "Track, organize, and showcase your Pokémon TCG collection with our powerful management tools.", "cta": "Get Started Now", "cardAlt1": "<PERSON><PERSON><PERSON>", "cardAlt2": "<PERSON><PERSON><PERSON>", "cardAlt3": "Mewtwo Card", "cardAlt4": "Blastoise Card"}, "features": {"badge": "Features", "title": "Everything You Need for Your Collection", "subtitle": "PokéCollector provides you with all the tools you need to manage your Pokémon card collection.", "search": {"title": "Card Search", "description": "Search and filter through thousands of Pokémon cards by set, type, rarity, and more."}, "collection": {"title": "Collection Management", "description": "Add cards to your collection, track quantities, and organize them however you prefer."}, "wishlist": {"title": "Wishlist", "description": "Keep track of cards you want to acquire with our wishlist feature."}, "customCollections": {"title": "Custom Collections", "description": "Create and manage multiple custom collections for different sets or themes."}, "filtering": {"title": "Advanced Filtering", "description": "Filter your collection by set, type, rarity, and more to find exactly what you're looking for."}, "realtime": {"title": "Real-time Updates", "description": "Your collection syncs across all your devices with real-time updates."}}, "cta": {"title": "Ready to Start Your Collection?", "subtitle": "Join thousands of Pokémon card collectors who are already using PokéCollector to manage their collections.", "pricingButton": "View Pricing Plans", "exploreButton": "Explore Cards"}}, "errors": {"generic": "An error has occurred", "notFound": "Not found", "unauthorized": "Unauthorized", "forbidden": "Forbidden", "validation": "Validation error", "network": "Network error", "server": "Server error"}, "onboarding": {"title": "Getting Started - PokéCollector", "description": "Interactive guide to start using PokéCollector and manage your Pokémon card collection", "welcome": "Welcome to PokéCollector!", "intro": "Your perfect companion for managing your Pokémon card collection. Here's a brief guide to the main features:", "startButton": "Start Collecting", "steps": {"search": {"title": "Search Cards", "description": "Explore thousands of Pokémon cards using our search and filtering tools."}, "collection": {"title": "Build Your Collection", "description": "Add cards to your collection with details like quantity, condition, and notes."}, "wishlist": {"title": "Create a Wishlist", "description": "Keep track of cards you want to add to your collection in the future."}}}, "footer": {"description": "The ultimate tool for managing your Pokémon TCG card collection, for collectors of all levels.", "features": {"title": "Features", "cardSearch": "Card Search", "collectionManagement": "Collection Management", "wishlist": "Wishlist"}, "resources": {"title": "Resources", "guides": "Guides", "rules": "Rules", "blog": "Blog", "tutorials": "Tutorials"}, "company": {"title": "Company", "about": "About", "contact": "Contact", "privacy": "Privacy Policy", "terms": "Terms of Service"}, "copyright": "All rights reserved.", "disclaimer": "Pokémon and Pokémon character names are trademarks of Nintendo. This site is not affiliated with Nintendo or The Pokémon Company."}, "sidebar": {"openMenu": "Open menu", "closeMenu": "Close menu"}, "pokemonTypes": {"colorless": "Colorless", "darkness": "Darkness", "dragon": "Dragon", "fairy": "Fairy", "fighting": "Fighting", "fire": "Fire", "grass": "Grass", "lightning": "Lightning", "metal": "Metal", "psychic": "Psychic", "water": "Water"}, "cardSupertypes": {"energy": "Energy", "pokemon": "Pokémon", "pokémon": "Pokémon", "trainer": "Trainer"}, "cardSubtypes": {"acespec": "ACE SPEC", "ancient": "Ancient", "baby": "Baby", "basic": "Basic", "break": "BREAK", "eternamax": "Eternamax", "ex": "EX", "fusion": "Fusion", "fusionStrike": "Fusion Strike", "future": "Future", "goldenrodGameCorner": "Goldenrod Game Corner", "gx": "GX", "item": "<PERSON><PERSON>", "legend": "LEGEND", "levelup": "Level-Up", "mega": "MEGA", "pokemontool": "Pokémon Tool", "pokemontoolF": "Pokémon Tool F", "prime": "Prime", "prismstar": "Prism Star", "radiant": "<PERSON><PERSON><PERSON>", "rapidstrike": "Rapid Strike", "restored": "Restored", "rocketsSecretMachine": "Rocket's Secret Machine", "singlestrike": "Single Strike", "sp": "SP", "special": "Special", "stadium": "Stadium", "stage1": "Stage 1", "stage2": "Stage 2", "star": "Star", "supporter": "Supporter", "tagteam": "TAG TEAM", "teamplasma": "Team Plasma", "technicalmachine": "Technical Machine", "tera": "<PERSON><PERSON>", "tool": "Tool", "ultrabeast": "Ultra Beast", "v": "V", "vmax": "VMAX", "vstar": "VSTAR", "vunion": "V-UNION", "rapid": "Rapid Strike", "single": "Single Strike", "ace": "ACE SPEC"}, "cardConditions": {"mint": "Mint", "nearmint": "Near Mint", "excellent": "Excellent", "good": "Good", "lightplayed": "Light Played", "played": "Played", "poor": "Poor"}, "cardFinishes": {"regular": "Regular", "foil": "Foil", "holo": "Holo", "reverseHolo": "Reverse Holo"}, "cardEditions": {"first": "First Edition", "unlimited": "Unlimited", "limited": "Limited"}, "cardRarities": {"Common": "Common", "Uncommon": "Uncommon", "Rare": "Rare", "RareHolo": "Rare Holo", "RareUltra": "Rare Ultra", "RareSecret": "Rare Secret", "RareRainbow": "Rare Rainbow", "RareHoloEX": "Rare Holo EX", "RareHoloV": "Rare Holo V", "RareHoloVMAX": "Rare Holo VMAX", "RareHoloVSTAR": "Rare Holo VSTAR", "RarePrism": "Rare Prism", "RarePrismStar": "Rare Prism Star", "RareShinyGX": "Rare Shiny GX", "AmazingRare": "Amazing Rare", "ClassicCollection": "Classic Collection", "Promo": "Promo", "IllustratorRare": "Illustrator Rare", "IllustrationRare": "Illustration Rare", "ShinyRare": "<PERSON>y <PERSON>", "TrainerGalleryRareHolo": "Trainer Gallery Rare Holo", "RareHoloLV.X": "Rare Holo LV.X", "UltraRare": "Ultra Rare", "DoubleRare": "Double Rare", "RareHoloGX": "Rare Holo GX", "ShinyUltraRare": "Shiny Ultra Rare", "SpecialIllustrationRare": "Special Illustration Rare", "RareHoloStar": "Rare Holo Star", "RareShiny": "Rare Shiny", "ACESPECRare": "ACE SPEC Rare", "HyperRare": "Hyper Rare", "LEGEND": "LEGEND", "RadiantRare": "Radiant Rare", "RareACE": "Rare ACE", "RareBREAK": "Rare BREAK", "RarePrime": "Rare Prime", "RareShining": "Rare Shining", "common": "Common", "uncommon": "Uncommon", "rare": "Rare", "rareholo": "Rare Holo", "rareultra": "Rare Ultra", "raresecret": "Rare Secret", "rarerainbow": "Rare Rainbow", "rareholoex": "Rare Holo EX", "rareholov": "Rare Holo V", "rareholovmax": "Rare Holo VMAX", "rareholovstar": "Rare Holo VSTAR", "rareprism": "Rare Prism", "rareprismstar": "Rare Prism Star", "rareshinygx": "Rare Shiny GX", "amazingrare": "Amazing Rare", "classiccollection": "Classic Collection", "promo": "Promo", "illustratorrare": "Illustrator Rare", "illustrationrare": "Illustration Rare", "shinyrare": "<PERSON>y <PERSON>", "trainergalleryrareholo": "Trainer Gallery Rare Holo", "rarehololvx": "Rare Holo LV.X", "ultrarare": "Ultra Rare", "doublerare": "Double Rare", "rarehologx": "Rare Holo GX", "shinyultrarare": "Shiny Ultra Rare", "specialillustrationrare": "Special Illustration Rare", "rareholostar": "Rare Holo Star", "rareshiny": "Rare Shiny", "acespecrare": "ACE SPEC Rare", "hyperrare": "Hyper Rare", "legend": "LEGEND", "radiantrare": "Radiant Rare", "rareace": "Rare ACE", "rarebreak": "Rare BREAK", "rareprime": "Rare Prime", "rareshining": "Rare Shining"}, "checkout": {"confirmSubscription": "Confirm subscription", "selectedPlan": "Selected plan:", "price": "Price:", "perMonth": "/month", "continueToPayment": "Continue to payment", "processingPayment": "Processing payment...", "error": "Payment processing error", "errorDescription": "Error connecting to payment service. Please try again.", "noCheckoutUrl": "No checkout URL received"}, "toasts": {"loginSuccess": "Successfully logged in", "logoutSuccess": "Successfully logged out", "registerSuccess": "Registration successful", "passwordResetEmailSent": "Password reset email sent", "passwordChanged": "Password changed successfully", "nameUpdated": "Name updated successfully", "accountDeleted": "Account deleted successfully", "collectionCreated": "Collection created successfully", "collectionUpdated": "Collection updated successfully", "collectionDeleted": "Collection deleted successfully", "cardAdded": "Card added to collection", "cardRemoved": "Card removed from collection", "cardUpdated": "Card updated in collection", "wishlistAdded": "Card added to wishlist", "wishlistRemoved": "Card removed from wishlist", "subscriptionUpdated": "Subscription updated successfully", "subscriptionCancelled": "Subscription cancelled successfully", "error": "An error occurred", "networkError": "Network error. Please check your connection", "unauthorized": "Unauthorized. Please log in again", "sessionExpired": "Your session has expired. Please log in again", "success": "Success!"}, "pagination": {"showing": "Showing", "of": "of", "results": "results", "previous": "Previous", "next": "Next", "morePages": "More pages", "goToPage": "Go to page", "goToPreviousPage": "Go to previous page", "goToNextPage": "Go to next page"}, "admin": {"title": "Admin Panel", "loading": "Loading admin panel...", "error": "Error loading admin panel", "unauthorized": "You don't have permission to access the admin panel", "loggedInAs": "Logged in as", "users": "Users", "subscriptions": "Subscriptions", "auditLogs": {"title": "<PERSON><PERSON>", "description": "Complete history of administrative actions", "loading": "Loading audit logs...", "noLogs": "No audit logs found", "totalLogs": "Total logs", "filters": {"title": "Filters & Search", "adminUser": "Admin User", "targetUser": "Target User", "action": "Action", "entityType": "Entity Type", "dateRange": "Date Range", "searchPlaceholder": "Search by email...", "allAdmins": "All Admins", "allTargets": "All Targets", "allActions": "All Actions", "allEntities": "All Entities", "dateFrom": "From", "dateTo": "To", "clearFilters": "Clear Filters", "applyFilters": "Apply Filters"}, "table": {"timestamp": "Timestamp", "admin": "Admin", "action": "Action", "entity": "Entity", "target": "Target", "details": "Details", "viewDetails": "View Details", "hideDetails": "Hide Details"}, "actions": {"CREATE": "Create", "UPDATE": "Update", "DELETE": "Delete", "LOGIN": "<PERSON><PERSON>", "LOGOUT": "Logout", "PLAN_CHANGE": "Plan Change", "STATUS_CHANGE": "Status Change", "OVERRIDE_CREATE": "Override Create", "OVERRIDE_DEACTIVATE": "Override Deactivate", "SYNC_STRIPE": "Sync Stripe", "CANCEL_SUBSCRIPTION": "Cancel Subscription"}, "entities": {"user": "User", "subscription": "Subscription", "collection": "Collection", "wishlist": "Wishlist", "override": "Override", "admin": "Admin"}, "details": {"title": "Action Details", "adminUser": "Admin User", "targetUser": "Target User", "timestamp": "Timestamp", "ipAddress": "IP Address", "userAgent": "User Agent", "metadata": "<PERSON><PERSON><PERSON>", "changes": "Changes", "oldValues": "Previous Values", "newValues": "New Values", "noChanges": "No changes recorded", "noMetadata": "No metadata available", "unknown": "Unknown"}, "export": {"button": "Export Logs", "csv": "Export to CSV", "exporting": "Exporting...", "success": "Logs exported successfully", "error": "Failed to export logs"}, "errors": {"loadFailed": "Failed to load audit logs", "exportFailed": "Failed to export audit logs", "filterError": "Error applying filters"}}, "settings": {"title": "<PERSON><PERSON>s", "description": "System configuration and administrative tools", "loading": "Loading settings...", "systemConfig": {"title": "System Configuration", "description": "Application settings and environment configuration", "environment": "Environment", "version": "Version", "buildDate": "Build Date", "nodeVersion": "Node Version", "databaseUrl": "Database URL", "apiUrl": "API URL", "frontendUrl": "Frontend URL", "features": "Feature Flags", "maintenance": "Maintenance Mode", "enabled": "Enabled", "disabled": "Disabled", "unknown": "Unknown"}, "adminUsers": {"title": "Admin Users", "description": "Manage administrator privileges and access", "totalAdmins": "Total Admins", "addAdmin": "Add Admin", "removeAdmin": "Remove <PERSON>", "searchAdmins": "Search admin users...", "grantAdmin": "Grant Admin Access", "revokeAdmin": "Revoke Admin Access", "confirmGrant": "Are you sure you want to grant admin privileges to this user?", "confirmRevoke": "Are you sure you want to revoke admin privileges from this user?", "adminGranted": "Admin privileges granted successfully", "adminRevoked": "Admin privileges revoked successfully", "cannotRevokeYourself": "You cannot revoke your own admin privileges", "lastLogin": "Last Login", "loginCount": "Login <PERSON>", "status": "Status", "actions": "Actions"}, "security": {"title": "Security Settings", "description": "Authentication and security configuration", "sessionTimeout": "Session Timeout", "maxLoginAttempts": "<PERSON> Login Attempts", "passwordPolicy": "Password Policy", "twoFactorAuth": "Two-Factor Authentication", "ipWhitelist": "IP Whitelist", "rateLimiting": "Rate Limiting", "auditLogging": "<PERSON>t Logging", "minutes": "minutes", "attempts": "attempts", "active": "Active", "inactive": "Inactive"}, "database": {"title": "Database Management", "description": "Database statistics and maintenance tools", "connectionStatus": "Connection Status", "totalTables": "Total Tables", "totalRecords": "Total Records", "databaseSize": "Database Size", "lastBackup": "Last Backup", "connected": "Connected", "disconnected": "Disconnected", "tables": "Tables", "records": "Records", "size": "Size", "runMaintenance": "Run Maintenance", "createBackup": "Create Backup", "viewLogs": "View Logs", "maintenanceRunning": "Running maintenance...", "maintenanceComplete": "Maintenance completed successfully", "backupCreated": "Backup created successfully"}, "apis": {"title": "API Configuration", "description": "External API settings and monitoring", "pokemonTcgApi": "Pokémon TCG API", "stripeApi": "Stripe API", "supabaseApi": "Supabase API", "status": "Status", "lastCheck": "Last Check", "responseTime": "Response Time", "requestCount": "Request Count", "errorRate": "Error Rate", "operational": "Operational", "degraded": "Degraded", "down": "Down", "testConnection": "Test Connection", "testing": "Testing...", "testSuccessful": "Connection test successful", "testFailed": "Connection test failed"}, "maintenance": {"title": "System Maintenance", "description": "System maintenance and cleanup tools", "clearCache": "<PERSON>ache", "cleanupLogs": "Cleanup Logs", "optimizeDatabase": "Optimize Database", "rebuildIndexes": "Rebuild Indexes", "cacheCleared": "<PERSON><PERSON> cleared successfully", "logsCleanedUp": "Logs cleaned up successfully", "databaseOptimized": "Database optimized successfully", "indexesRebuilt": "Indexes rebuilt successfully", "lastCacheClean": "Last Cache Clean", "lastLogCleanup": "Last Log Cleanup", "lastOptimization": "Last Optimization", "systemHealth": "System Health", "healthy": "Healthy", "warning": "Warning", "critical": "Critical"}, "errors": {"loadFailed": "Failed to load settings", "updateFailed": "Failed to update settings", "maintenanceFailed": "Maintenance operation failed", "testFailed": "Connection test failed", "unauthorized": "Unauthorized to perform this action"}, "success": {"settingsUpdated": "Settings updated successfully", "operationComplete": "Operation completed successfully"}}, "userManagement": "User Management", "subscriptionManagement": "Subscription Management", "userManagementPlaceholder": "User Management component will be implemented here", "subscriptionManagementPlaceholder": "Subscription Management component will be implemented here", "auditLogsDescription": "Track and monitor all administrative actions for compliance and security", "settingsDescription": "Configure system settings and manage administrative functions", "loadingUsers": "Loading users...", "totalUsers": "Total users", "filtersAndSearch": "Filters & Search", "searchUsers": "Search users...", "adminStatus": "Admin Status", "allUsers": "All Users", "adminsOnly": "Admins Only", "regularUsers": "Regular Users", "activeStatus": "Active Status", "allStatuses": "All Statuses", "activeUsers": "Active Users", "inactiveUsers": "Inactive Users", "planType": "Plan Type", "allPlans": "All Plans", "user": "User", "plan": "Plan", "status": "Status", "lastLogin": "Last Login", "statistics": "Statistics", "actions": "Actions", "admin": "Admin", "inactive": "Inactive", "logins": "logins", "cards": "cards", "collections": "collections", "wishlist": "wishlist", "never": "Never", "userDetails": "User Details", "basicInfo": "Basic Information", "email": "Email", "fullName": "Full Name", "notProvided": "Not provided", "registrationDate": "Registration Date", "loginCount": "Login <PERSON>", "regularUser": "Regular User", "active": "Active", "subscriptionInfo": "Subscription Information", "stripeSubscription": "Stripe Subscription", "notConnected": "Not connected", "noSubscription": "No subscription found", "userStatistics": "User Statistics", "totalCards": "Total Cards", "totalCollections": "Total Collections", "totalWishlistItems": "Total Wishlist Items", "noStatistics": "No statistics available", "editUser": "Edit User", "editUserDescription": "Make changes to the user's information and permissions.", "enterFullName": "Enter full name", "adminPrivileges": "Admin Privileges", "activeAccount": "Active Account", "notes": "Notes", "enterNotes": "Enter admin notes...", "deleteUser": "Delete User", "deleteUserWarning": "This action cannot be undone. This will permanently delete the user account and all associated data.", "deleteConfirmation": "Type 'DELETE' to confirm this action", "success": "Success", "userUpdated": "User updated successfully", "userDeleted": "User deleted successfully", "usersLoadError": "Failed to load users", "userDetailsError": "Failed to load user details", "userUpdateError": "Failed to update user", "userDeleteError": "Failed to delete user", "andMore": "And {{count}} more...", "subscriptionManagementDescription": "Manage user subscriptions, plans, and billing", "userSearch": "User Search", "enterUserEmail": "Enter user email...", "search": "Search", "userNotFound": "User Not Found", "userNotFoundDescription": "No user found with that email address", "searchError": "Failed to search for user", "subscriptionDetails": "Subscription Details", "userInfo": "User Information", "currentSubscription": "Current Subscription", "changePlan": "Change Plan", "changeStatus": "Change Status", "syncStripe": "Sync Stripe", "isActive": "Is Active", "currentPeriodStart": "Current Period Start", "currentPeriodEnd": "Current Period End", "cancelAtPeriodEnd": "Cancel at Period End", "yes": "Yes", "no": "No", "stripeSubscriptionId": "Stripe Subscription ID", "stripeCustomerId": "Stripe Customer ID", "createSubscription": "Create Subscription", "activeOverrides": "Active Overrides", "createOverride": "Create Override", "reason": "Reason", "expires": "Expires", "noOverrides": "No active overrides", "dangerZone": "Danger Zone", "cancelSubscription": "Cancel Subscription", "cancelSubscriptionDescription": "Permanently cancel the user's subscription", "cancel": "Cancel", "changePlanDescription": "Change the user's subscription plan. This action will be logged for audit purposes.", "newPlan": "New Plan", "selectPlan": "Select a plan", "enterReason": "Enter reason for plan change...", "planChanged": "Plan changed successfully", "planChangeError": "Failed to change plan", "changeStatusDescription": "Change the subscription status. This will affect the user's access to features.", "newStatus": "New Status", "selectStatus": "Select a status", "paused": "Paused", "canceled": "Canceled", "statusChanged": "Subscription status changed successfully", "statusChangeError": "Failed to change subscription status", "syncStripeDescription": "Synchronize subscription data with Stripe. This will fetch the latest information from Stripe and update the local database.", "syncWarning": "This will overwrite local subscription data with information from Stripe. Make sure this is what you want to do.", "syncNow": "Sync Now", "syncCompleted": "Stripe synchronization completed", "syncError": "Failed to sync with <PERSON><PERSON>", "cancelSubscriptionWarning": "This will cancel the user's subscription. Choose whether to cancel immediately or at the end of the current billing period.", "cancelImmediately": "Cancel immediately (user loses access now)", "enterCancelReason": "Enter reason for cancellation...", "cancelConfirmation": "This action cannot be undone. The user will lose access to premium features.", "subscriptionCancelled": "Subscription cancelled successfully", "cancelError": "Failed to cancel subscription", "createOverrideDescription": "Create a temporary override for subscription limits or features.", "overrideType": "Override Type", "selectOverrideType": "Select override type", "planChange": "Plan Change", "limitOverride": "Limit Override", "featureAccess": "Feature Access", "billingOverride": "Billing Override", "originalValue": "Original Value", "enterOriginalValue": "Enter original value...", "overrideValue": "Override Value", "enterOverrideValue": "Enter override value...", "expirationDate": "Expiration Date (Optional)", "enterOverrideReason": "Enter reason for override...", "overrideCreated": "Override created successfully", "overrideError": "Failed to create override", "fillAllFields": "Please fill in all required fields"}}